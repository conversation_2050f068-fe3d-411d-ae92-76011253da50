//
//  MessageManager.swift
//  ChatAdvisor
//
//  Created by AI Assistant on 2025/01/29.
//

import Foundation
import OSLog
import Combine

private let logger = Logger(subsystem: "com.sanva.chatadvisor", category: "MessageManager")

/// 消息管理器 - 负责消息的CRUD操作和业务逻辑
@MainActor
class MessageManager: ObservableObject {
    // MARK: - Published Properties
    @Published var messages: [ChatMessage] = []
    @Published var isLoading: Bool = false
    @Published var hasMoreMessages: Bool = true
    
    // MARK: - Private Properties
    private let chatRepository: ChatRepositoryProtocol
    private let messageRepository: MessageRepositoryProtocol
    private let cacheManager: CacheManager<ChatMessage>
    private var _currentChatId: String = ""
    private let pageSize: Int = 20

    // MARK: - Public Properties
    /// 当前聊天ID（只读）
    var currentChatId: String { _currentChatId }
    
    // MARK: - Initialization
    init(chatRepository: ChatRepositoryProtocol, 
         messageRepository: MessageRepositoryProtocol,
         cacheManager: CacheManager<ChatMessage>) {
        self.chatRepository = chatRepository
        self.messageRepository = messageRepository
        self.cacheManager = cacheManager
    }
    
    // MARK: - Public Methods
    
    /// 设置当前聊天ID并加载消息
    func setChatId(_ chatId: String) async {
        guard chatId != _currentChatId else { return }

        _currentChatId = chatId
        messages.removeAll()
        hasMoreMessages = true

        await loadInitialMessages()
    }

    /// 加载初始消息
    func loadInitialMessages() async {
        guard !_currentChatId.isEmpty else { return }
        
        isLoading = true
        
        do {
            let loadedMessages = try await messageRepository.fetchMessages(
                chatId: _currentChatId,
                limit: pageSize,
                before: []
            )
            
            messages = loadedMessages
            hasMoreMessages = loadedMessages.count == pageSize
            
            logger.info("加载初始消息成功: \(loadedMessages.count) 条")
        } catch {
            logger.error("加载初始消息失败: \(error.localizedDescription)")
        }
        
        isLoading = false
    }
    
    /// 加载更多消息（分页）
    func loadMoreMessages() async {
        guard !isLoading && hasMoreMessages && !_currentChatId.isEmpty else { return }

        isLoading = true

        do {
            let moreMessages = try await messageRepository.fetchMessages(
                chatId: _currentChatId,
                limit: pageSize,
                before: messages
            )
            
            // 将新消息插入到列表开头（因为是历史消息）
            messages.insert(contentsOf: moreMessages, at: 0)
            hasMoreMessages = moreMessages.count == pageSize
            
            logger.info("加载更多消息成功: \(moreMessages.count) 条")
        } catch {
            logger.error("加载更多消息失败: \(error.localizedDescription)")
        }
        
        isLoading = false
    }
    
    /// 添加新消息
    func addMessage(_ message: ChatMessage) async {
        // 检查消息是否已存在
        guard !messages.contains(where: { $0.id == message.id }) else {
            logger.warning("消息已存在，跳过添加: \(message.id)")
            return
        }
        
        // 添加到内存列表
        messages.append(message)
        
        // 异步保存到数据库
        do {
            try await messageRepository.saveMessage(message)
            logger.debug("消息保存成功: \(message.id)")
        } catch {
            logger.error("消息保存失败: \(error.localizedDescription)")
            // 保存失败时从内存中移除
            messages.removeAll { $0.id == message.id }
        }
    }
    
    /// 更新消息
    func updateMessage(_ message: ChatMessage) async {
        // 更新内存中的消息
        if let index = messages.firstIndex(where: { $0.id == message.id }) {
            messages[index] = message
        }
        
        // 异步更新数据库
        do {
            try await messageRepository.updateMessage(message)
            logger.debug("消息更新成功: \(message.id)")
        } catch {
            logger.error("消息更新失败: \(error.localizedDescription)")
        }
    }
    
    /// 删除消息
    func deleteMessage(id: String) async {
        // 从内存中移除
        messages.removeAll { $0.id == id }
        
        // 异步从数据库删除
        do {
            try await messageRepository.deleteMessage(id: id)
            logger.debug("消息删除成功: \(id)")
        } catch {
            logger.error("消息删除失败: \(error.localizedDescription)")
        }
    }
    
    /// 批量添加消息
    func addMessages(_ newMessages: [ChatMessage]) async {
        let uniqueMessages = newMessages.filter { newMessage in
            !messages.contains { $0.id == newMessage.id }
        }
        
        guard !uniqueMessages.isEmpty else { return }
        
        // 添加到内存列表
        messages.append(contentsOf: uniqueMessages)
        
        // 异步批量保存到数据库
        do {
            try await messageRepository.batchSaveMessages(uniqueMessages)
            logger.debug("批量消息保存成功: \(uniqueMessages.count) 条")
        } catch {
            logger.error("批量消息保存失败: \(error.localizedDescription)")
            // 保存失败时从内存中移除
            for message in uniqueMessages {
                messages.removeAll { $0.id == message.id }
            }
        }
    }
    
    /// 获取指定角色的消息
    func getMessages(for role: Role) -> [ChatMessage] {
        return messages.filter { $0.role == role }
    }
    
    /// 获取用户消息（排除系统消息）
    func getUserMessages() -> [ChatMessage] {
        return messages.filter { $0.role != .system }
    }
    
    /// 获取最后一条消息
    func getLastMessage() -> ChatMessage? {
        return messages.last
    }
    
    /// 获取最后一条用户消息
    func getLastUserMessage() -> ChatMessage? {
        return messages.last { $0.role == .user }
    }
    
    /// 获取最后一条助手消息
    func getLastAssistantMessage() -> ChatMessage? {
        return messages.last { $0.role == .assistant }
    }
    
    /// 检查是否有未完成的消息
    func hasIncompleteMessage() -> Bool {
        return messages.contains { !$0.isComplete }
    }
    
    /// 获取未完成的消息
    func getIncompleteMessages() -> [ChatMessage] {
        return messages.filter { !$0.isComplete }
    }
    
    /// 标记消息为完成状态
    func markMessageAsComplete(id: String) async {
        guard let index = messages.firstIndex(where: { $0.id == id }) else { return }
        
        var message = messages[index]
        message.isComplete = true
        messages[index] = message
        
        // 异步更新数据库
        await updateMessage(message)
    }
    
    /// 清空当前聊天的所有消息
    func clearMessages() async {
        let chatId = _currentChatId
        messages.removeAll()

        guard !chatId.isEmpty else { return }

        // 异步从数据库删除
        do {
            try await messageRepository.deleteAllMessages(chatId: chatId)
            logger.info("清空聊天消息成功: \(chatId)")
        } catch {
            logger.error("清空聊天消息失败: \(error.localizedDescription)")
        }
    }

    // MARK: - AI响应消息专用方法

    /// 创建新的AI助手消息（用于流式响应开始）
    func createAssistantMessage(chatId: String, messageId: String) async -> ChatMessage {
        let message = ChatMessage(
            id: messageId,
            chatId: chatId,
            role: .assistant,
            content: "",
            isComplete: false,
            messageType: .text
        )

        // 添加到内存列表
        messages.append(message)

        // 异步保存到数据库
        do {
            try await messageRepository.saveMessage(message)
            logger.debug("AI消息创建成功: \(message.id)")
        } catch {
            logger.error("AI消息创建失败: \(error.localizedDescription)")
            // 创建失败时从内存中移除
            messages.removeAll { $0.id == message.id }
        }

        return message
    }

    /// 流式更新AI消息内容
    func appendContentToMessage(messageId: String, content: String) async {
        guard !content.isEmpty else { return }

        // 查找并更新内存中的消息
        guard let index = messages.firstIndex(where: { $0.id == messageId }) else {
            logger.warning("未找到要更新的消息: \(messageId)")
            return
        }

        // 更新消息内容
        messages[index].append(content: content)

        // 异步更新数据库
        do {
            try await messageRepository.updateMessage(messages[index])
            logger.debug("消息内容更新成功: \(messageId)")
        } catch {
            logger.error("消息内容更新失败: \(error.localizedDescription)")
        }
    }

    /// 标记AI消息为完成状态并更新完成原因
    func completeAssistantMessage(messageId: String, finishReason: FinishReason? = nil) async {
        guard let index = messages.firstIndex(where: { $0.id == messageId }) else {
            logger.warning("未找到要完成的消息: \(messageId)")
            return
        }

        // 更新消息状态
        messages[index].isComplete = true
        messages[index].finishReason = finishReason

        // 异步更新数据库
        do {
            try await messageRepository.updateMessage(messages[index])
            logger.debug("AI消息完成状态更新成功: \(messageId)")
        } catch {
            logger.error("AI消息完成状态更新失败: \(error.localizedDescription)")
        }
    }

    /// 获取指定ID的消息
    func getMessage(id: String) -> ChatMessage? {
        return messages.first { $0.id == id }
    }

    /// 同步消息到外部数组（用于与currentChat.messages同步）
    func syncMessagesToArray(_ targetArray: inout [ChatMessage]) {
        // 增量同步：只更新有差异的消息
        var needsUpdate = false

        // 检查数量是否一致
        if targetArray.count != messages.count {
            needsUpdate = true
        } else {
            // 检查内容是否一致
            for (index, message) in messages.enumerated() {
                if index < targetArray.count {
                    if targetArray[index].id != message.id ||
                       targetArray[index].content != message.content ||
                       targetArray[index].isComplete != message.isComplete {
                        needsUpdate = true
                        break
                    }
                }
            }
        }

        // 只有在需要时才更新
        if needsUpdate {
            targetArray = messages
            logger.debug("消息数组同步完成，消息数量: \(self.messages.count)")
        }
    }
    
    /// 搜索消息
    func searchMessages(keyword: String) async -> [ChatMessage] {
        guard !keyword.isEmpty else { return [] }
        
        do {
            let results = try await messageRepository.searchMessages(keyword: keyword, chatId: currentChatId)
            logger.debug("消息搜索完成: \(results.count) 条结果")
            return results
        } catch {
            logger.error("消息搜索失败: \(error.localizedDescription)")
            return []
        }
    }
    
    /// 获取消息统计信息
    func getMessageStats() -> (total: Int, userMessages: Int, assistantMessages: Int) {
        let userCount = messages.filter { $0.role == .user }.count
        let assistantCount = messages.filter { $0.role == .assistant }.count
        return (total: messages.count, userMessages: userCount, assistantMessages: assistantCount)
    }
}
