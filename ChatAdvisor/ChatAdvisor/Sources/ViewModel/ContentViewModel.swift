//
//  ContentViewModel.swift
//  JunShi
//
//  Created by md on 2024/5/23.
//

import Combine
import Foundation
import SwiftUI
import OSLog

private let logger = Logger(subsystem: "com.sanva.chatadvisor", category: "ContentViewModel")
enum ChatError: LocalizedError {
    case invalidChatID
    case chatViewModelNotFound
    case stateInconsistency
    case databaseError(String)
    case networkError(String)

    var errorDescription: String? {
        switch self {
        case .invalidChatID:
            return "无效的会话ID"
        case .chatViewModelNotFound:
            return "会话视图模型未找到"
        case .stateInconsistency:
            return "状态不一致"
        case .databaseError(let message):
            return "数据库错误: \(message)"
        case .networkError(let message):
            return "网络错误: \(message)"
        }
    }
}

@MainActor
class ContentViewModel: ObservableObject {
    @Published var chatViewModels: [ChatViewModel] = []
    @Published var selectedChatID: String? = nil
    @Published var isLoadingChatDetails: Bool = false
    @Published var chatLoadingError: String? = nil

    var currentChatViewModel: ChatViewModel? {
        // 优先从ChatSessionManager获取
        if let currentSession = ChatSessionManager.shared.currentSession {
            return currentSession.viewModel
        }
        // 回退到原有逻辑
        return chatViewModels.first { $0.currentChat.id == selectedChatID }
    }

    private var cancellable = Set<AnyCancellable>()
    private let maxCachedViewModels = 5 // 限制缓存的ChatViewModel数量
    private lazy var sessionManager = ChatSessionManager.shared
    private var isInitialized = false
    private var preloadTask: Task<Void, Never>?

    init() {
        NotificationCenter.default.publisher(for: .logout)
            .sink { [weak self] _ in
                guard let self else { return }
                chatViewModels = []
                selectedChatID = nil
                isLoadingChatDetails = false
                isInitialized = false
                preloadTask?.cancel()
            }.store(in: &cancellable)

        // 启动预加载
        startPreloading()
    }

    /// 等待预加载完成，但设置超时避免无限等待
    private func waitForPreloadingWithTimeout() async {
        let timeout: TimeInterval = 2.0 // 最多等待2秒
        let startTime = Date()

        while !isInitialized && Date().timeIntervalSince(startTime) < timeout {
            try? await Task.sleep(nanoseconds: 100_000_000) // 100ms
        }
    }

    /// 启动时预加载数据，减少用户感知的加载时间
    private func startPreloading() {
        preloadTask = Task {
            do {
                // 预加载数据库连接和基础数据
                _ = AdvisorDatabaseManager.shared.database

                // 预查询最后一个聊天ID（不设置UI状态）
                let lastChatId = AdvisorDatabaseManager.shared.getLastChatId()

                if let lastChatId = lastChatId {
                    // 预加载聊天数据（但不设置为当前会话）
                    _ = await AdvisorDatabaseManager.shared.fetchChat(id: lastChatId)
                }

                await MainActor.run {
                    isInitialized = true
                }
            } catch {
                // 预加载失败不影响正常流程
                print("预加载失败: \(error)")
            }
        }
    }

    func initializeIfNeeded(chatListViewModel: ChatListViewModel) {
        // 只在第一次初始化时设置selectedChatID，且确保启动管理器已完成初始化
        guard selectedChatID == nil && chatViewModels.isEmpty else { return }
        guard AppStartupManager.shared.isStartupComplete else { return }

        Task {
            do {
                // 由于AppStartupManager已经完成了数据库初始化和预加载，这里直接获取最后的聊天
                let lastChatId = AdvisorDatabaseManager.shared.getLastChatId()
                if let lastChatId = lastChatId {
                    // 立即设置selectedChatID
                    await MainActor.run {
                        selectedChatID = lastChatId
                        isLoadingChatDetails = true
                    }

                    // 使用ChatSessionManager加载会话（数据可能已经在缓存中）
                    try await sessionManager.switchToSession(chatId: lastChatId, chatListViewModel: chatListViewModel)

                    // 更新本地状态
                    await MainActor.run {
                        if let currentSession = sessionManager.currentSession,
                           let viewModel = currentSession.viewModel {
                            chatViewModels.append(viewModel)
                        }
                        logger.info("快速初始化聊天会话完成: \(lastChatId)")
                    }
                }
            } catch {
                await MainActor.run {
                    chatLoadingError = "初始化会话失败: \(error.localizedDescription)"
                    selectedChatID = nil // 出错时重置
                    logger.error("快速初始化聊天会话失败: \(error.localizedDescription)")
                }
            }

            await MainActor.run {
                isLoadingChatDetails = false
            }
        }
    }

    deinit {
        cancellable.forEach { $0.cancel() }
        preloadTask?.cancel()
    }

    // MARK: - ChatViewModel Management

    func getOrCreateChatViewModel(for chat: Chat, chatListViewModel: ChatListViewModel) -> ChatViewModel {
        // 清除之前的错误
        chatLoadingError = nil

        // 查找现有的ChatViewModel
        if let existingViewModel = chatViewModels.first(where: { $0.currentChat.id == chat.id }) {
            return existingViewModel
        }

        // 创建新的ChatViewModel
        let chatViewModel = ChatViewModel(chatListViewModel: chatListViewModel)
        chatViewModel.currentModel = ChatViewModel.allModels.first ?? ChatsModel.default
        chatViewModel.setCurrentChat(chat: chat)

        // 添加到数组并管理缓存大小
        chatViewModels.append(chatViewModel)
        manageCacheSize()

        return chatViewModel
    }

    private func manageCacheSize() {
        // 如果超过最大缓存数量，移除最旧的（除了当前选中的）
        while chatViewModels.count > maxCachedViewModels {
            if let indexToRemove = chatViewModels.firstIndex(where: { $0.currentChat.id != selectedChatID }) {
                chatViewModels.remove(at: indexToRemove)
            } else {
                break
            }
        }
    }

    func selectChat(_ chatID: String) {
        selectedChatID = chatID
    }

    @MainActor
    func selectChatAtomically(chat: Chat, chatListViewModel: ChatListViewModel) async throws {
        // 使用ChatSessionManager进行原子化的会话选择

        // 清除之前的错误状态
        chatLoadingError = nil
        isLoadingChatDetails = true

        do {
            // 1. 验证输入参数
            guard !chat.id.isEmpty else {
                throw ChatError.invalidChatID
            }

            logger.info("开始原子化会话选择: chatId=\(chat.id)")

            // 2. 使用ChatSessionManager切换会话
            try await sessionManager.switchToSession(chatId: chat.id, chatListViewModel: chatListViewModel)

            // 3. 更新本地状态以保持兼容性
            selectedChatID = chat.id

            // 4. 确保chatViewModels包含当前会话的ViewModel
            if let currentSession = sessionManager.currentSession,
               let viewModel = currentSession.viewModel,
               !chatViewModels.contains(where: { $0.currentChat.id == chat.id }) {
                chatViewModels.append(viewModel)
                manageCacheSize()
            }

            // 5. 验证最终状态
            guard currentChatViewModel?.currentChat.id == chat.id else {
                logger.error("会话切换后状态不一致: 期望=\(chat.id), 实际=\(self.currentChatViewModel?.currentChat.id ?? "nil")")
                throw ChatError.stateInconsistency
            }

            isLoadingChatDetails = false
            logger.info("会话选择成功完成: chatId=\(chat.id)")

        } catch {
            let errorMessage = "会话选择失败: \(error.localizedDescription)"
//            logger.error(errorMessage)
            chatLoadingError = errorMessage
            isLoadingChatDetails = false

            // 如果是新会话且切换失败，至少设置基本状态让用户能继续操作
            if selectedChatID == nil || selectedChatID != chat.id {
                selectedChatID = chat.id
                logger.info("设置备用会话状态: chatId=\(chat.id)")
            }

            throw error
        }
    }
    
    /// {{ AURA: Modify - 修复会话选择方法，确保与ChatSessionManager同步 }}
    @MainActor
    func selectChatOptimized(chat: Chat, chatListViewModel: ChatListViewModel) async throws {
        // 优化的会话选择实现，确保ChatSessionManager状态同步
        
        // 清除之前的错误状态
        chatLoadingError = nil
        isLoadingChatDetails = true
        
        do {
            // 1. 快速验证
            guard !chat.id.isEmpty else {
                throw ChatError.invalidChatID
            }
            
            logger.info("开始优化会话选择: chatId=\(chat.id)")
            
            // 2. 使用ChatSessionManager进行会话切换，确保状态同步
            try await sessionManager.switchToSession(chatId: chat.id, chatListViewModel: chatListViewModel)
            
            // 3. 更新本地状态以保持兼容性
            selectedChatID = chat.id
            
            // 4. 确保chatViewModels包含当前会话的ViewModel
            if let currentSession = sessionManager.currentSession,
               let viewModel = currentSession.viewModel,
               !chatViewModels.contains(where: { $0.currentChat.id == chat.id }) {
                chatViewModels.append(viewModel)
                manageCacheSize()
            }
            
            // 5. 验证最终状态
            guard currentChatViewModel?.currentChat.id == chat.id else {
                logger.error("会话切换后状态不一致: 期望=\(chat.id), 实际=\(self.currentChatViewModel?.currentChat.id ?? "nil")")
                throw ChatError.stateInconsistency
            }
            
            isLoadingChatDetails = false
            logger.info("优化会话选择完成: chatId=\(chat.id)")
            
        } catch {
            let errorMessage = "优化会话选择失败: \(error.localizedDescription)"
            chatLoadingError = errorMessage
            isLoadingChatDetails = false
            logger.error("\(errorMessage)")
            
            // 回退到基本状态
            selectedChatID = chat.id
            throw error
        }
    }

    func removeChatViewModel(chatID: String) {
        chatViewModels.removeAll { $0.currentChat.id == chatID }
        if selectedChatID == chatID {
            selectedChatID = chatViewModels.first?.currentChat.id
        }
    }
}
