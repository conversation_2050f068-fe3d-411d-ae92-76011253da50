//
//  NetworkStatusManager.swift
//  ChatAdvisor
//
//  Created by AI Assistant on 2025/06/27.
//

import Foundation
import Network
import OSLog
import SwiftUI

private let logger = Logger(subsystem: "com.sanva.chatadvisor", category: "NetworkStatusManager")

/// 网络连接状态
enum NetworkConnectionStatus {
    case connected
    case disconnected
    case connecting
    case reconnecting
    
    var displayText: String {
        switch self {
        case .connected:
            return "已连接"
        case .disconnected:
            return "连接断开"
        case .connecting:
            return "连接中..."
        case .reconnecting:
            return "重新连接中..."
        }
    }
    
    var color: Color {
        switch self {
        case .connected:
            return .green
        case .disconnected:
            return .red
        case .connecting, .reconnecting:
            return .orange
        }
    }
}

/// 网络状态管理器
class NetworkStatusManager: ObservableObject {
    static let shared = NetworkStatusManager()
    
    @Published var isNetworkAvailable: Bool = true
    @Published var connectionStatus: NetworkConnectionStatus = .disconnected
    @Published var shouldShowNetworkStatus: Bool = false
    private var isUserInitiatedConnection: Bool = false  // 新增：标记是否为用户主动发起的连接
    
    private let monitor = NWPathMonitor()
    private let queue = DispatchQueue(label: "NetworkMonitor")
    private var retryCount = 0
    private let maxRetryCount = 5
    private var retryTimer: Timer?
    
    private init() {
        startMonitoring()
    }
    
    deinit {
        stopMonitoring()
    }
    
    /// 开始网络监控
    private func startMonitoring() {
        monitor.pathUpdateHandler = { [weak self] path in
            DispatchQueue.main.async {
                self?.updateNetworkStatus(path: path)
            }
        }
        monitor.start(queue: queue)
    }
    
    /// 停止网络监控
    private func stopMonitoring() {
        monitor.cancel()
        retryTimer?.invalidate()
    }
    
    /// 更新网络状态
    private func updateNetworkStatus(path: NWPath) {
        let wasAvailable = isNetworkAvailable
        isNetworkAvailable = path.status == .satisfied
        
        logger.info("网络状态更新: \(self.isNetworkAvailable ? "可用" : "不可用")")
        
        // 网络状态变化时的处理
        if !wasAvailable && isNetworkAvailable {
            // 网络恢复
            connectionStatus = .connected
            retryCount = 0
            hideNetworkStatusAfterDelay()
        } else if wasAvailable && !isNetworkAvailable {
            // 网络断开
            connectionStatus = .disconnected
            shouldShowNetworkStatus = true
        }
    }
    
    /// 设置连接状态
    func setConnectionStatus(_ status: NetworkConnectionStatus, isUserInitiated: Bool = false) {
        DispatchQueue.main.async { [weak self] in
            guard let self = self else { return }
            self.connectionStatus = status
            self.isUserInitiatedConnection = isUserInitiated

            switch status {
            case .connected:
                self.retryCount = 0
                // 连接成功后立即隐藏状态指示器
                self.shouldShowNetworkStatus = false
                self.isUserInitiatedConnection = false
            case .disconnected:
                // 只有在网络真正断开时才显示，而不是正常的连接结束
                if !self.isNetworkAvailable {
                    self.shouldShowNetworkStatus = true
                }
            case .connecting, .reconnecting:
                // 只有在用户主动发起连接或重连时才显示指示器
                self.shouldShowNetworkStatus = isUserInitiated || status == .reconnecting
            }
        }
    }
    
    /// 尝试重连
    func attemptReconnection(completion: @escaping (Bool) -> Void) {
        guard isNetworkAvailable else {
            completion(false)
            return
        }
        
        guard retryCount < maxRetryCount else {
            logger.warning("达到最大重连次数，停止重连")
            setConnectionStatus(.disconnected)
            completion(false)
            return
        }
        
        retryCount += 1
        setConnectionStatus(.reconnecting, isUserInitiated: true)  // 重连时显示状态
        
        logger.info("尝试第 \(self.retryCount) 次重连")
        
        // 指数退避策略
        let delay = min(pow(2.0, Double(retryCount)), 30.0)
        
        DispatchQueue.main.asyncAfter(deadline: .now() + delay) { [weak self] in
            guard let self = self else {
                completion(false)
                return
            }
            
            if self.isNetworkAvailable {
                completion(true)
            } else {
                self.attemptReconnection(completion: completion)
            }
        }
    }
    
    /// 重置重连计数
    func resetRetryCount() {
        retryCount = 0
    }
    
    /// 延迟隐藏网络状态指示器
    private func hideNetworkStatusAfterDelay() {
        DispatchQueue.main.asyncAfter(deadline: .now() + 1.5) { [weak self] in
            self?.shouldShowNetworkStatus = false
        }
    }

    /// 立即隐藏网络状态指示器
    func hideNetworkStatus() {
        DispatchQueue.main.async { [weak self] in
            self?.shouldShowNetworkStatus = false
        }
    }
}

/// 网络状态指示器视图
struct NetworkStatusIndicator: View {
    @ObservedObject var networkManager = NetworkStatusManager.shared
    
    var body: some View {
        if networkManager.shouldShowNetworkStatus {
            HStack(spacing: 8) {
                // 状态图标
                if networkManager.connectionStatus == .connecting || networkManager.connectionStatus == .reconnecting {
                    ProgressView()
                        .scaleEffect(0.7)
                        .tint(networkManager.connectionStatus.color)
                } else {
                    Circle()
                        .fill(networkManager.connectionStatus.color)
                        .frame(width: 8, height: 8)
                }
                
                // 状态文本
                Text(networkManager.connectionStatus.displayText)
                    .font(.caption)
                    .foregroundColor(networkManager.connectionStatus.color)
                
                Spacer()
                
                // 手动重连按钮（仅在断开连接时显示）
                if networkManager.connectionStatus == .disconnected && networkManager.isNetworkAvailable {
                    Button("重连") {
                        networkManager.attemptReconnection { success in
                            if success {
                                networkManager.setConnectionStatus(.connected)
                            }
                        }
                    }
                    .font(.caption)
                    .foregroundColor(.blue)
                }
            }
            .padding(.horizontal, 16)
            .padding(.vertical, 8)
            .background(Color(.systemGray6))
            .cornerRadius(8)
            .padding(.horizontal, AppThemes.padding)
            .transition(.move(edge: .top).combined(with: .opacity))
            .animation(.easeInOut(duration: 0.3), value: networkManager.shouldShowNetworkStatus)
        }
    }
}

#Preview {
    VStack {
        NetworkStatusIndicator()
        Spacer()
    }
}
