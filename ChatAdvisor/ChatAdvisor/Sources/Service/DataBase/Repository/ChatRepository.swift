//
//  ChatRepository.swift
//  ChatAdvisor
//
//  Created by AI Assistant on 2025/01/29.
//

import Foundation
import WCDBSwift
import OSLog

private let logger = Logger(subsystem: "com.sanva.chatadvisor", category: "ChatRepository")

/// 聊天数据仓库实现
class ChatRepository: ChatRepositoryProtocol, @unchecked Sendable {
    private let database: Database
    private let databaseQueue: DispatchQueue
    private let cacheManager: CacheManager<Chat>
    private let notificationCenter: DataChangeNotificationCenter

    init(database: Database, databaseQueue: DispatchQueue, cacheManager: CacheManager<Chat>) {
        self.database = database
        self.databaseQueue = databaseQueue
        self.cacheManager = cacheManager
        self.notificationCenter = DataChangeNotificationCenter.shared
    }
    
    // MARK: - ChatRepositoryProtocol Implementation
    
    func fetchChats(limit: Int, offset: Int, isArchived: Bool) async throws -> [Chat] {
        return try await withCheckedThrowingContinuation { continuation in
            databaseQueue.async { [weak self] in
                guard let self = self else {
                    continuation.resume(throwing: RepositoryError.databaseNotInitialized)
                    return
                }
                
                do {
                    let chats: [Chat] = try self.database.getObjects(
                        fromTable: "chats",
                        where: Chat.Properties.isArchived == isArchived,
                        orderBy: [Chat.Properties.createdTime.order(.descending)],
                        limit: limit,
                        offset: offset
                    )
                    
                    // 缓存获取的聊天
                    Task {
                        for chat in chats {
                            await self.cacheManager.set(key: chat.id, value: chat, expiration: 300) // 5分钟缓存
                        }
                    }
                    
                    continuation.resume(returning: chats)
                } catch {
                    logger.error("获取聊天列表失败: \(error.localizedDescription)")
                    continuation.resume(throwing: RepositoryError.operationFailed(error))
                }
            }
        }
    }
    
    func fetchChat(id: String) async throws -> Chat? {
        // 先从缓存获取
        if let cachedChat = await cacheManager.get(key: id) {
            logger.debug("从缓存获取聊天: \(id)")
            return cachedChat
        }
        
        return try await withCheckedThrowingContinuation { continuation in
            databaseQueue.async { [weak self] in
                guard let self = self else {
                    continuation.resume(throwing: RepositoryError.databaseNotInitialized)
                    return
                }
                
                do {
                    let chat: Chat? = try self.database.getObject(
                        fromTable: "chats",
                        where: Chat.Properties.id == id
                    )
                    
                    // 缓存获取的聊天
                    if let chat = chat {
                        Task {
                            await self.cacheManager.set(key: chat.id, value: chat, expiration: 300)
                        }
                    }
                    
                    continuation.resume(returning: chat)
                } catch {
                    logger.error("获取聊天失败: \(error.localizedDescription)")
                    continuation.resume(throwing: RepositoryError.operationFailed(error))
                }
            }
        }
    }
    
    func fetchChatSync(id: String) -> Chat? {
        do {
            return try database.getObject(fromTable: "chats", where: Chat.Properties.id == id)
        } catch {
            logger.error("同步获取聊天失败: \(error.localizedDescription)")
            return nil
        }
    }
    
    func saveChat(_ chat: Chat) async throws {
        try await withCheckedThrowingContinuation { (continuation: CheckedContinuation<Void, Error>) in
            databaseQueue.async { [weak self] in
                guard let self = self else {
                    continuation.resume(throwing: RepositoryError.databaseNotInitialized)
                    return
                }
                
                do {
                    try self.database.insert(chat, intoTable: "chats")

                    // 更新缓存
                    Task {
                        await self.cacheManager.set(key: chat.id, value: chat, expiration: 300)
                    }

                    // 发布数据变更通知
                    self.notificationCenter.chatCreated(chat.id)

                    logger.debug("保存聊天成功: \(chat.id)")
                    continuation.resume()
                } catch {
                    logger.error("保存聊天失败: \(error.localizedDescription)")
                    continuation.resume(throwing: RepositoryError.operationFailed(error))
                }
            }
        }
    }
    
    func updateChat(_ chat: Chat) async throws {
        try await withCheckedThrowingContinuation { (continuation: CheckedContinuation<Void, Error>) in
            databaseQueue.async { [weak self] in
                guard let self = self else {
                    continuation.resume(throwing: RepositoryError.databaseNotInitialized)
                    return
                }
                
                do {
                    try self.database.update(
                        table: "chats",
                        on: Chat.Properties.all,
                        with: chat,
                        where: Chat.Properties.id == chat.id
                    )
                    
                    // 更新缓存
                    Task {
                        await self.cacheManager.set(key: chat.id, value: chat, expiration: 300)
                    }

                    // 发布数据变更通知
                    self.notificationCenter.chatUpdated(chat.id)

                    logger.debug("更新聊天成功: \(chat.id)")
                    continuation.resume()
                } catch {
                    logger.error("更新聊天失败: \(error.localizedDescription)")
                    continuation.resume(throwing: RepositoryError.operationFailed(error))
                }
            }
        }
    }
    
    func deleteChat(id: String) async throws {
        try await withCheckedThrowingContinuation { (continuation: CheckedContinuation<Void, Error>) in
            databaseQueue.async { [weak self] in
                guard let self = self else {
                    continuation.resume(throwing: RepositoryError.databaseNotInitialized)
                    return
                }
                
                do {
                    try self.database.delete(fromTable: "chats", where: Chat.Properties.id == id)
                    
                    // 从缓存中移除
                    Task {
                        await self.cacheManager.remove(key: id)
                    }

                    // 发布数据变更通知
                    self.notificationCenter.chatDeleted(id)

                    logger.debug("删除聊天成功: \(id)")
                    continuation.resume()
                } catch {
                    logger.error("删除聊天失败: \(error.localizedDescription)")
                    continuation.resume(throwing: RepositoryError.operationFailed(error))
                }
            }
        }
    }
    
    func archiveChat(id: String, isArchived: Bool) async throws {
        try await withCheckedThrowingContinuation { (continuation: CheckedContinuation<Void, Error>) in
            databaseQueue.async { [weak self] in
                guard let self = self else {
                    continuation.resume(throwing: RepositoryError.databaseNotInitialized)
                    return
                }
                
                do {
                    try self.database.update(
                        table: "chats",
                        on: Chat.Properties.isArchived,
                        with: isArchived,
                        where: Chat.Properties.id == id
                    )
                    
                    // 从缓存中移除，强制下次重新加载
                    Task {
                        await self.cacheManager.remove(key: id)
                    }
                    
                    logger.debug("归档聊天成功: \(id), isArchived: \(isArchived)")
                    continuation.resume()
                } catch {
                    logger.error("归档聊天失败: \(error.localizedDescription)")
                    continuation.resume(throwing: RepositoryError.operationFailed(error))
                }
            }
        }
    }
    
    func searchChats(keyword: String, isArchived: Bool) async throws -> [Chat] {
        return try await withCheckedThrowingContinuation { continuation in
            databaseQueue.async { [weak self] in
                guard let self = self else {
                    continuation.resume(throwing: RepositoryError.databaseNotInitialized)
                    return
                }
                
                do {
                    let chats: [Chat] = try self.database.getObjects(
                        fromTable: "chats",
                        where: Chat.Properties.title.like("%\(keyword)%") && Chat.Properties.isArchived == isArchived,
                        orderBy: [Chat.Properties.createdTime.order(.descending)]
                    )
                    
                    continuation.resume(returning: chats)
                } catch {
                    logger.error("搜索聊天失败: \(error.localizedDescription)")
                    continuation.resume(throwing: RepositoryError.operationFailed(error))
                }
            }
        }
    }
    
    func getChatCount(isArchived: Bool) async throws -> Int {
        return try await withCheckedThrowingContinuation { continuation in
            databaseQueue.async { [weak self] in
                guard let self = self else {
                    continuation.resume(throwing: RepositoryError.databaseNotInitialized)
                    return
                }
                
                do {
                    let count = try self.database.getValue(
                        on: Chat.Properties.id.count(),
                        fromTable: "chats",
                        where: Chat.Properties.isArchived == isArchived
                    ).int32Value
                    
                    continuation.resume(returning: Int(count))
                } catch {
                    logger.error("获取聊天数量失败: \(error.localizedDescription)")
                    continuation.resume(throwing: RepositoryError.operationFailed(error))
                }
            }
        }
    }
    
    func cleanupEmptyChats() async throws {
        try await withCheckedThrowingContinuation { (continuation: CheckedContinuation<Void, Error>) in
            databaseQueue.async { [weak self] in
                guard let self = self else {
                    continuation.resume(throwing: RepositoryError.databaseNotInitialized)
                    return
                }
                
                do {
                    // 查找没有用户消息的聊天
                    let emptyChats: [Chat] = try self.database.getObjects(fromTable: "chats")
                        .filter { chat in
                            let userMessages = chat.messages.filter { $0.role == .user }
                            return userMessages.isEmpty
                        }
                    
                    // 删除空聊天
                    for chat in emptyChats {
                        try self.database.delete(fromTable: "chats", where: Chat.Properties.id == chat.id)
                        Task {
                            await self.cacheManager.remove(key: chat.id)
                        }
                    }
                    
                    logger.info("清理空聊天完成，删除了 \(emptyChats.count) 个空聊天")
                    continuation.resume()
                } catch {
                    logger.error("清理空聊天失败: \(error.localizedDescription)")
                    continuation.resume(throwing: RepositoryError.operationFailed(error))
                }
            }
        }
    }
    
    func batchDeleteChats(ids: [String]) async throws {
        try await withCheckedThrowingContinuation { (continuation: CheckedContinuation<Void, Error>) in
            databaseQueue.async { [weak self] in
                guard let self = self else {
                    continuation.resume(throwing: RepositoryError.databaseNotInitialized)
                    return
                }
                
                do {
                    try self.database.run(transaction: { _ in
                        for id in ids {
                            try self.database.delete(fromTable: "chats", where: Chat.Properties.id == id)
                        }
                    })
                    
                    // 从缓存中批量移除
                    Task {
                        for id in ids {
                            await self.cacheManager.remove(key: id)
                        }
                    }
                    
                    logger.debug("批量删除聊天成功，删除了 \(ids.count) 个聊天")
                    continuation.resume()
                } catch {
                    logger.error("批量删除聊天失败: \(error.localizedDescription)")
                    continuation.resume(throwing: RepositoryError.operationFailed(error))
                }
            }
        }
    }
}
