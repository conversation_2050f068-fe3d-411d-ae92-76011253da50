//
//  CacheManager.swift
//  ChatAdvisor
//
//  Created by AI Assistant on 2025/01/29.
//

import Foundation
import OSLog
import UIKit

private let logger = Logger(subsystem: "com.sanva.chatadvisor", category: "CacheManager")

/// 缓存项包装器
class CacheItem<T: Codable>: Codable {
    let value: T
    let expiration: Date?
    let createdAt: Date

    init(value: T, expiration: TimeInterval?) {
        self.value = value
        self.createdAt = Date()
        self.expiration = expiration.map { Date().addingTimeInterval($0) }
    }

    var isExpired: Bool {
        guard let expiration = expiration else { return false }
        return Date() > expiration
    }

    // MARK: - Codable实现
    private enum CodingKeys: String, CodingKey {
        case value
        case expiration
        case createdAt
    }

    required init(from decoder: Decoder) throws {
        let container = try decoder.container(keyedBy: CodingKeys.self)
        self.value = try container.decode(T.self, forKey: .value)
        self.expiration = try container.decodeIfPresent(Date.self, forKey: .expiration)
        self.createdAt = try container.decode(Date.self, forKey: .createdAt)
    }

    func encode(to encoder: Encoder) throws {
        var container = encoder.container(keyedBy: CodingKeys.self)
        try container.encode(value, forKey: .value)
        try container.encodeIfPresent(expiration, forKey: .expiration)
        try container.encode(createdAt, forKey: .createdAt)
    }
}

/// 磁盘缓存管理器
class DiskCache<T: Codable> {
    private let cacheDirectory: URL
    private let fileManager = FileManager.default
    private let queue = DispatchQueue(label: "com.sanva.chatadvisor.diskcache", qos: .utility)
    
    init(name: String) throws {
        let cachesDirectory = fileManager.urls(for: .cachesDirectory, in: .userDomainMask).first!
        cacheDirectory = cachesDirectory.appendingPathComponent("ChatAdvisor").appendingPathComponent(name)
        
        try fileManager.createDirectory(at: cacheDirectory, withIntermediateDirectories: true)
    }
    
    private func fileURL(for key: String) -> URL {
        let fileName = key.addingPercentEncoding(withAllowedCharacters: .alphanumerics) ?? key
        return cacheDirectory.appendingPathComponent(fileName).appendingPathExtension("cache")
    }
    
    func get(key: String) async -> T? {
        return await withCheckedContinuation { continuation in
            queue.async { [weak self] in
                guard let self = self else {
                    continuation.resume(returning: nil)
                    return
                }
                
                let fileURL = self.fileURL(for: key)
                
                guard self.fileManager.fileExists(atPath: fileURL.path) else {
                    continuation.resume(returning: nil)
                    return
                }
                
                do {
                    let data = try Data(contentsOf: fileURL)
                    let cacheItem = try JSONDecoder().decode(CacheItem<T>.self, from: data)
                    
                    if cacheItem.isExpired {
                        try? self.fileManager.removeItem(at: fileURL)
                        continuation.resume(returning: nil)
                    } else {
                        continuation.resume(returning: cacheItem.value)
                    }
                } catch {
                    logger.error("磁盘缓存读取失败: \(error.localizedDescription)")
                    continuation.resume(returning: nil)
                }
            }
        }
    }
    
    func set(key: String, value: T, expiration: TimeInterval?) async {
        await withCheckedContinuation { (continuation: CheckedContinuation<Void, Never>) in
            queue.async { [weak self] in
                guard let self = self else {
                    continuation.resume()
                    return
                }
                
                let fileURL = self.fileURL(for: key)
                let cacheItem = CacheItem(value: value, expiration: expiration)
                
                do {
                    let data = try JSONEncoder().encode(cacheItem)
                    try data.write(to: fileURL)
                    logger.debug("磁盘缓存写入成功: \(key)")
                } catch {
                    logger.error("磁盘缓存写入失败: \(error.localizedDescription)")
                }
                
                continuation.resume()
            }
        }
    }
    
    func remove(key: String) async {
        await withCheckedContinuation { (continuation: CheckedContinuation<Void, Never>) in
            queue.async { [weak self] in
                guard let self = self else {
                    continuation.resume()
                    return
                }
                
                let fileURL = self.fileURL(for: key)
                try? self.fileManager.removeItem(at: fileURL)
                continuation.resume()
            }
        }
    }
    
    func clear() async {
        await withCheckedContinuation { (continuation: CheckedContinuation<Void, Never>) in
            queue.async { [weak self] in
                guard let self = self else {
                    continuation.resume()
                    return
                }
                
                do {
                    let contents = try self.fileManager.contentsOfDirectory(at: self.cacheDirectory, includingPropertiesForKeys: nil)
                    for fileURL in contents {
                        try self.fileManager.removeItem(at: fileURL)
                    }
                    logger.info("磁盘缓存清理完成")
                } catch {
                    logger.error("磁盘缓存清理失败: \(error.localizedDescription)")
                }
                
                continuation.resume()
            }
        }
    }
}

/// 多级缓存管理器
class CacheManager<T: Codable & Identifiable> {
    private let memoryCache: NSCache<NSString, CacheItem<T>>
    private let diskCache: DiskCache<T>
    private let maxMemoryItems: Int
    private let defaultExpiration: TimeInterval
    private let accessQueue = DispatchQueue(label: "com.sanva.chatadvisor.cache.access", attributes: .concurrent)
    
    init(name: String, maxMemoryItems: Int = 100, defaultExpiration: TimeInterval = 300) throws {
        self.maxMemoryItems = maxMemoryItems
        self.defaultExpiration = defaultExpiration
        self.memoryCache = NSCache<NSString, CacheItem<T>>()
        self.diskCache = try DiskCache<T>(name: name)
        
        setupMemoryCache()
        setupMemoryWarningObserver()
    }
    
    private func setupMemoryCache() {
        memoryCache.countLimit = maxMemoryItems
        memoryCache.totalCostLimit = maxMemoryItems * 1024 // 假设每个对象约1KB
    }
    
    private func setupMemoryWarningObserver() {
        NotificationCenter.default.addObserver(
            forName: UIApplication.didReceiveMemoryWarningNotification,
            object: nil,
            queue: .main
        ) { [weak self] _ in
            self?.memoryCache.removeAllObjects()
            logger.info("收到内存警告，清理内存缓存")
        }
    }
    
    /// 获取缓存值
    func get(key: String) async -> T? {
        return await withCheckedContinuation { continuation in
            accessQueue.async { [weak self] in
                guard let self = self else {
                    continuation.resume(returning: nil)
                    return
                }
                
                let nsKey = NSString(string: key)
                
                // 1. 先检查内存缓存
                if let cacheItem = self.memoryCache.object(forKey: nsKey) {
                    if !cacheItem.isExpired {
                        logger.debug("内存缓存命中: \(key)")
                        continuation.resume(returning: cacheItem.value)
                        return
                    } else {
                        self.memoryCache.removeObject(forKey: nsKey)
                    }
                }
                
                // 2. 检查磁盘缓存
                Task {
                    if let diskValue = await self.diskCache.get(key: key) {
                        logger.debug("磁盘缓存命中: \(key)")
                        
                        // 将磁盘缓存的值提升到内存缓存
                        let cacheItem = CacheItem(value: diskValue, expiration: self.defaultExpiration)
                        self.memoryCache.setObject(cacheItem, forKey: nsKey)
                        
                        continuation.resume(returning: diskValue)
                    } else {
                        logger.debug("缓存未命中: \(key)")
                        continuation.resume(returning: nil)
                    }
                }
            }
        }
    }
    
    /// 设置缓存值
    func set(key: String, value: T, expiration: TimeInterval? = nil) async {
        let finalExpiration = expiration ?? defaultExpiration
        let nsKey = NSString(string: key)
        let cacheItem = CacheItem(value: value, expiration: finalExpiration)
        
        await withCheckedContinuation { (continuation: CheckedContinuation<Void, Never>) in
            accessQueue.async(flags: .barrier) { [weak self] in
                guard let self = self else {
                    continuation.resume()
                    return
                }
                
                // 1. 设置内存缓存
                self.memoryCache.setObject(cacheItem, forKey: nsKey)
                
                // 2. 异步设置磁盘缓存
                Task {
                    await self.diskCache.set(key: key, value: value, expiration: finalExpiration)
                    logger.debug("缓存设置完成: \(key)")
                }
                
                continuation.resume()
            }
        }
    }
    
    /// 移除缓存值
    func remove(key: String) async {
        let nsKey = NSString(string: key)
        
        await withCheckedContinuation { (continuation: CheckedContinuation<Void, Never>) in
            accessQueue.async(flags: .barrier) { [weak self] in
                guard let self = self else {
                    continuation.resume()
                    return
                }
                
                // 1. 从内存缓存移除
                self.memoryCache.removeObject(forKey: nsKey)
                
                // 2. 异步从磁盘缓存移除
                Task {
                    await self.diskCache.remove(key: key)
                    logger.debug("缓存移除完成: \(key)")
                }
                
                continuation.resume()
            }
        }
    }
    
    /// 清空所有缓存
    func clear() async {
        await withCheckedContinuation { (continuation: CheckedContinuation<Void, Never>) in
            accessQueue.async(flags: .barrier) { [weak self] in
                guard let self = self else {
                    continuation.resume()
                    return
                }
                
                // 1. 清空内存缓存
                self.memoryCache.removeAllObjects()
                
                // 2. 异步清空磁盘缓存
                Task {
                    await self.diskCache.clear()
                    logger.info("所有缓存清理完成")
                }
                
                continuation.resume()
            }
        }
    }
    
    /// 获取缓存统计信息
    func getCacheStats() -> (memoryCount: Int, memorySize: Int) {
        return (
            memoryCount: memoryCache.countLimit,
            memorySize: memoryCache.totalCostLimit
        )
    }
    
    deinit {
        NotificationCenter.default.removeObserver(self)
    }
}
