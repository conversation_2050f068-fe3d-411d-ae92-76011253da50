//
//  AppStartupManager.swift
//  ChatAdvisor
//
//  Created by Assistant on 2024/12/28.
//  管理应用启动状态和数据预加载
//

import Foundation
import SwiftUI
import OSLog

private let logger = Logger(subsystem: "com.sanva.chatadvisor", category: "AppStartup")

/// 应用启动管理器，负责协调启动时的数据加载
@MainActor
class AppStartupManager: ObservableObject {
    static let shared = AppStartupManager()
    
    /// 启动阶段状态
    enum StartupPhase {
        case launching      // 正在启动
        case initializing   // 正在初始化数据
        case ready         // 准备完成
        case failed(Error) // 启动失败
    }
    
    @Published var currentPhase: StartupPhase = .launching
    @Published var isStartupComplete: Bool = false
    
    // 启动完成的标记，用于确保只初始化一次
    private var hasInitialized = false
    
    private init() {}
    
    /// 标记启动完成（用于未登录状态或SplashViewModel同步）
    func markStartupComplete() async {
        await MainActor.run {
            currentPhase = .ready
            isStartupComplete = true
            hasInitialized = true  // 防止重复初始化
            logger.info("应用启动完成")
        }
    }
    
    /// 初始化应用数据（用于已登录状态）
    func initializeAppData() async {
        // {{ AURA-X: Modify - 防止与SplashViewModel重复初始化. Approval: mcp-feedback-enhanced(ID:20250129006). }}
        // 防止重复初始化，如果SplashViewModel已经完成了初始化，直接返回
        if hasInitialized || isStartupComplete {
            logger.info("初始化已完成，跳过重复初始化")
            return
        }
        hasInitialized = true
        
        logger.info("开始初始化应用数据")
        
        await MainActor.run {
            currentPhase = .initializing
        }
        
        do {
            // 1. 初始化数据库连接
            logger.info("初始化数据库连接...")
            _ = AdvisorDatabaseManager.shared.database
            
            // 2. 等待数据库准备完成
            let isDatabaseReady = await AdvisorDatabaseManager.shared.waitForDatabaseReady()
            guard isDatabaseReady else {
                throw AppStartupError.databaseNotReady
            }
            
            // 3. 预加载最后的聊天数据
            logger.info("预加载聊天数据...")
            let lastChatId = AdvisorDatabaseManager.shared.getLastChatId()
            
            if let lastChatId = lastChatId {
                // 预加载聊天数据到缓存中
                _ = await AdvisorDatabaseManager.shared.fetchChat(id: lastChatId)
                logger.info("预加载聊天数据完成: \(lastChatId)")
            }
            
            // 4. 标记启动完成
            await MainActor.run {
                currentPhase = .ready
                isStartupComplete = true
                logger.info("应用数据初始化完成")
            }
            
        } catch {
            logger.error("应用数据初始化失败: \(error.localizedDescription)")
            await MainActor.run {
                currentPhase = .failed(error)
                isStartupComplete = true // 即使失败也要标记完成，避免无限等待
            }
        }
    }
    
    /// 重置启动状态（用于退出登录时）
    func reset() {
        hasInitialized = false
        currentPhase = .launching
        isStartupComplete = false
        logger.info("重置启动状态")
    }
}

/// 启动错误类型
enum AppStartupError: LocalizedError {
    case databaseNotReady
    case initializationTimeout
    
    var errorDescription: String? {
        switch self {
        case .databaseNotReady:
            return "数据库未准备就绪"
        case .initializationTimeout:
            return "初始化超时"
        }
    }
}