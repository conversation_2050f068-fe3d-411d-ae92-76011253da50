//
//  BootsManager.swift
//  JunShi
//
//  Created by md on 2024/5/16.
//

import Foundation
import Moya
import Network
import OSLog
import StoreKit

private let logger = Logger(subsystem: "com.sanva.chatadvisor", category: "BootConfigManager")

class BootManager: NSObject, ObservableObject {
    static let shared = BootManager()
    @Published var isLoading = false
    @Published var isChina: Bool = false
    @Published var config: Configs = .default
    @Published var questions: [Question] = []
    @Published var allChatModels: [ChatsModel] = []

    override init() {
        super.init()
        isChina = SKPaymentQueue.default().storefront?.countryCode == "CHN"
        SKPaymentQueue.default().add(self)
    }

    func getConfig(forceRefresh: Bool = false) {
        // {{ AURA-X: Modify - 添加重复请求防护机制. Approval: mcp-feedback-enhanced(ID:20250129001). }}
        APIRequestManager.shared.executeRequestIfNeeded(
            endpoint: "/getConfig",
            forceRefresh: forceRefresh
        ) { [weak self] in
            guard let self else { return }
            
            executeOnMainThread {
                self.isLoading = true
            }
            
            NetworkService.shared.requestMulti(ConfigTarget.getConfig) { [weak self] (result: Result<NetworkResponse<Configs>, NetworkError>) in
                guard let self else { return }
                
                // 使用APIRequestManager处理结果
                APIRequestManager.shared.handleRequestResult(
                    endpoint: "/getConfig",
                    result: result,
                    onSuccess: { [weak self] response in
                        guard let self else { return }
                        DispatchQueue.main.async { [weak self] in
                            guard let self else { return }
                            if response.isSuccess {
                                self.config = response.data ?? Configs.default
                                logger.info("配置获取成功")
                            } else if self.config == nil {
                                self.config = Configs.default
                            }
                            self.isLoading = false
                        }
                    },
                    onFailure: { [weak self] error in
                        guard let self else { return }
                        DispatchQueue.main.async { [weak self] in
                            guard let self else { return }
                            logger.error("getConfig error: \(error)")
                            // 如果网络请求失败且config为nil，设置默认配置
                            if self.config == nil {
                                self.config = Configs.default
                            }
                            self.isLoading = false
                        }
                    }
                )
            }
        }
    }



    /// 主动检查版本更新
    /// - Parameter force: 是否强制检查
    func checkForUpdates(force: Bool = false) {
        logger.info("主动检查版本更新...")
        VersionUpdateManager.shared.checkForUpdates(force: force)
    }

    func getPricing(forceRefresh: Bool = false) {
        // {{ AURA-X: Modify - 添加重复请求防护机制. Approval: mcp-feedback-enhanced(ID:20250129001). }}
        APIRequestManager.shared.executeRequestIfNeeded(
            endpoint: "/getPricing",
            forceRefresh: forceRefresh
        ) { [weak self] in
            guard let self else { return }
            
            NetworkService.shared.requestMulti(PricingTarget.getPricing) { [weak self] (result: Result<NetworkResponse<[ChatsModel]>, NetworkError>) in
                guard let self else { return }
                
                // 使用APIRequestManager处理结果
                APIRequestManager.shared.handleRequestResult(
                    endpoint: "/getPricing",
                    result: result,
                    onSuccess: { [weak self] response in
                        guard let self else { return }
                        DispatchQueue.main.async { [weak self] in
                            guard let self else { return }
                            if response.isSuccess {
                                self.allChatModels = response.data ?? []
                                ChatViewModel.allModels = response.data ?? []
                            } else {
                                self.allChatModels = []
                            }
                        }
                    },
                    onFailure: { [weak self] error in
                        guard let self else { return }
                        logger.error("getPricing failed: \(error.localizedDescription)")
                    }
                )
            }
        }
    }
}

extension BootManager: SKPaymentTransactionObserver {
    func paymentQueue(_: SKPaymentQueue, updatedTransactions _: [SKPaymentTransaction]) {}

    func paymentQueueDidChangeStorefront(_ queue: SKPaymentQueue) {
        DispatchQueue.main.async { [weak self] in
            guard let self else { return }
            isChina = queue.storefront?.countryCode == "CHN"
        }
    }

    func executeOnMainThread(_ block: @escaping () -> Void) {
        if Thread.isMainThread {
            block()
        } else {
            DispatchQueue.main.async {
                block()
            }
        }
    }
}
