import SwiftUI

struct InputBottomView: View {
    @EnvironmentObject var chatViewModel: ChatViewModel
    @StateObject private var inputViewModel: InputViewModel = .init()

    var body: some View {
        VStack {
            Divider()
            switch inputViewModel.inputViewMode {
            case .text:
                HStack {
                    Button(action: {
                        withAnimation {
                            inputViewModel.inputViewMode = .recording
                        }
                    }) {
                        Image(systemName: "mic")
                            .padding(.horizontal)
                            .foregroundColor(.mainDark)
                    }
                    TextField("", text: $inputViewModel.textInput)
                        .placeholder("请输入".localized(), when: inputViewModel.textInput.isEmpty == true, color: .mainLight)
                        .foregroundColor(.mainDark)
                        .padding(.leading, AppThemes.padding)
                        .frame(height: 40)
                        .background(
                            RoundedRectangle(cornerRadius: 5)
                                .stroke(Color.mainDark, lineWidth: 1)
                        )
                    <PERSON><PERSON>(action: {
                        let viewModel = chatViewModel
                        viewModel.textInput = inputViewModel.textInput
                        viewModel.sendMessage()
                        if AccountManager.shared.currentUser != nil {
                            inputViewModel.textInput = ""
                        }
                    }) {
                        Image(systemName: "paperplane.fill")
                            .foregroundColor(.mainDark)
                            .padding(.horizontal)
                    }
                    .disabled(chatViewModel.isAnswering)
                }
                .padding(.vertical, AppThemes.padding / 2)
            case .recording:
                ZStack {
                    if inputViewModel.isPlaying == false, inputViewModel.isRecording == false {
                        HStack {
                            Button(action: {
                                withAnimation {
                                    inputViewModel.inputViewMode = .text
                                    inputViewModel.resetRecording()
                                }
                            }) {
                                Image(systemName: "keyboard")
                                    .foregroundColor(.mainDark)
                            }
                            Spacer()
                        }
                    }
                    HStack {
                        Spacer()
                        if inputViewModel.isPreview {
                            PlaybackButton()
                                .environmentObject(inputViewModel)
                        } else {
                            RecordingButton()
                                .environmentObject(inputViewModel)
                        }
                        Spacer()
                    }
                    if inputViewModel.isPreview, inputViewModel.isPlaying == false, inputViewModel.isRecording == false {
                        HStack {
                            Spacer()
                            Button(action: {
                                inputViewModel.resetRecording()
                            }) {
                                Image(systemName: "trash.fill")
                                    .foregroundColor(.mainDark)
                            }
                            Button(action: {
                                let viewModel = chatViewModel
                                viewModel.sendRecording(assetURL: inputViewModel.audioFileURL)
                                inputViewModel.resetRecording()
                            }) {
                                Image(systemName: "paperplane.fill")
                                    .foregroundColor(.mainDark)
                                    .padding(.trailing)
                            }
                            .disabled(chatViewModel.isAnswering)
                            .padding(.leading)
                        }
                    }
                }
                .onChange(of: chatViewModel.isAnswering) { value in
                    if value {
                        inputViewModel.resetRecording()
                    }
                }
                .padding(.vertical, AppThemes.padding / 2)
//                .frame(height: 64)
            }
        }
    }
}

struct RecordingButton: View {
    @EnvironmentObject var inputChatModel: InputViewModel
    @EnvironmentObject var chatViewModel: ChatViewModel

    var body: some View {
        ZStack {
            if inputChatModel.isRecording {
                Circle()
                    .stroke(lineWidth: 3)
                    .foregroundColor(Color(uiColor: .systemGray5))
                    .frame(width: 44, height: 44)
                Circle()
                    .trim(from: 0, to: inputChatModel.recordingDuration / inputChatModel.maxRecordingDuration)
                    .stroke(lineWidth: 3)
                    .foregroundColor(.mainDark)
                    .frame(width: 44, height: 44)
                    .rotationEffect(.degrees(-90))
                    .animation(.linear, value: inputChatModel.recordingDuration)
            }
            Image(systemName: inputChatModel.isRecording ? "stop.fill" : "record.circle")
                .font(.system(size: 24))
                .foregroundColor(.red)
        }
        .onTapGesture {
            if inputChatModel.isRecording {
                inputChatModel.stopRecording()
            } else {
                inputChatModel.startRecording { success in
                    if !success {
                        // 读取InfoPlist的NSMicrophoneUsageDescription
                        let message = Bundle.main.object(forInfoDictionaryKey: "NSMicrophoneUsageDescription") as? String ?? "请允许访问麦克风".localized()
                        let viewModel = chatViewModel
                        viewModel.failedToast(message)
                    } else {
                        inputChatModel.isRecording.toggle()
                    }
                }
            }
        }
    }
}

struct PlaybackButton: View {
    @EnvironmentObject var inputChatModel: InputViewModel

    var body: some View {
        ZStack {
            if inputChatModel.isPlaying {
                Circle()
                    .stroke(lineWidth: 3)
                    .foregroundColor(Color(uiColor: .systemGray5))
                    .frame(width: 44, height: 44)
                Circle()
                    .trim(from: 0, to: inputChatModel.playbackDuration / inputChatModel.maxRecordingDuration)
                    .stroke(lineWidth: 3)
                    .foregroundColor(.mainDark)
                    .frame(width: 44, height: 44)
                    .rotationEffect(.degrees(-90))
                    .animation(.linear, value: inputChatModel.playbackDuration)
            }
            Image(systemName: inputChatModel.isPlaying ? "stop.fill" : "play.fill")
                .font(.system(size: 24))
                .foregroundColor(.red)
        }
        .onTapGesture {
            if inputChatModel.isPlaying {
                inputChatModel.stopPlayback()
            } else {
                inputChatModel.startPlayback()
            }
        }
    }
}
