//
//  SplashView.swift
//  ChatAdvisor
//
//  Created by AI Assistant on 2025/01/02.
//

import SwiftUI

/// 开屏界面 - 在此期间完成所有初始化工作
struct SplashView: View {
    @StateObject private var splashViewModel = SplashViewModel()
    @Binding var isInitialized: Bool
    
    var body: some View {
        ZStack {
            // 背景渐变
            LinearGradient(
                gradient: Gradient(colors: [
                    Color.accentColor.opacity(0.8),
                    Color.accentColor.opacity(0.6),
                    Color.accentColor.opacity(0.4)
                ]),
                startPoint: .topLeading,
                endPoint: .bottomTrailing
            )
            .ignoresSafeArea()
            
            VStack(spacing: 30) {
                Spacer()
                
                // Logo 和应用名称
                VStack(spacing: 20) {
                    // Logo 动画
                    Image(systemName: "message.circle.fill")
                        .font(.system(size: 80))
                        .foregroundColor(.white)
                        .scaleEffect(splashViewModel.logoScale)
                        .animation(.easeInOut(duration: 1.5).repeatForever(autoreverses: true), value: splashViewModel.logoScale)
                    
                    Text("ChatAdvisor")
                        .font(.largeTitle)
                        .fontWeight(.bold)
                        .foregroundColor(.white)
                        .opacity(splashViewModel.textOpacity)
                        .animation(.easeInOut(duration: 1.0).delay(0.5), value: splashViewModel.textOpacity)
                }
                
                Spacer()
                
                // 初始化进度
                VStack(spacing: 16) {
                    // 进度条
                    ProgressView(value: splashViewModel.progress, total: 1.0)
                        .progressViewStyle(LinearProgressViewStyle(tint: .white))
                        .scaleEffect(x: 1, y: 2, anchor: .center)
                        .frame(width: 200)
                    
                    // 状态文本
                    Text(splashViewModel.statusText)
                        .font(.subheadline)
                        .foregroundColor(.white.opacity(0.8))
                        .multilineTextAlignment(.center)
                        .animation(.easeInOut(duration: 0.3), value: splashViewModel.statusText)
                }
                .padding(.bottom, 50)
            }
            .padding()
        }
        .onAppear {
            splashViewModel.startInitialization { success in
                if success {
                    // 初始化完成，延迟一点时间让用户看到完成状态
                    DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
                        withAnimation(.easeInOut(duration: 0.5)) {
                            isInitialized = true
                        }
                    }
                } else {
                    // 初始化失败，可以显示错误或重试
                    // 这里暂时也标记为完成，让用户进入主界面
                    DispatchQueue.main.asyncAfter(deadline: .now() + 1.0) {
                        withAnimation(.easeInOut(duration: 0.5)) {
                            isInitialized = true
                        }
                    }
                }
            }
        }
    }
}

// MARK: - Preview

struct SplashView_Previews: PreviewProvider {
    static var previews: some View {
        SplashView(isInitialized: .constant(false))
    }
}
