import Localize_Swift
import StepperView
import SwiftUI
import OSLog
private let logger = Logger(subsystem: "com.sanva.chatadvisor", category: "MultiStepFormView")
private let itemWidth: CGFloat = 20

struct MultiStepFormView: View {
    var completeAction: (() -> Void)? = nil

    @EnvironmentObject var chatViewModel: ChatViewModel
    @EnvironmentObject var contentViewModel: ContentViewModel
    @EnvironmentObject var chatListViewModel: ChatListViewModel

    var body: some View {
        NavigationStack {
            ZStack {
                VStack(alignment: .leading) {
                    Divider()
                    Section(header: Color.clear.frame(height: 24)) {
                        HStack {
                            Spacer()
                            StepperView()
                                .addSteps(chatViewModel.stepFormViewModel.steps.enumerated().map { index, step in
                                    createStepTextView(index: index, step: step)
                                })
                                .indicators(chatViewModel.stepFormViewModel.steps.enumerated().map { index, element in
                                    createIndicator(index: index, element: element)
                                })
                                .stepIndicatorMode(StepperMode.horizontal)
                                .lineOptions(StepperLineOptions.rounded(4, 8, .mainDark))
                                .stepLifeCycles(chatViewModel.stepFormViewModel.steps.map { $0.isComplete ? StepLifeCycle.completed : StepLifeCycle.pending })
                                .spacing(calculateSpacing())
                                .padding(.top, AppThemes.padding)
                            //                                .frame(width: UIScreen.main.bounds.width, height: 44)
                            Spacer()
                        }
                        //                    .frame(height: 50)
                    }

                    TabView(selection: $chatViewModel.stepFormViewModel.currentStep) {
                        ForEach(chatViewModel.stepFormViewModel.stepViewModels.indices, id: \.self) { index in
                            StepView(viewModel: chatViewModel.stepFormViewModel.stepViewModels[index])
                                .tag(index)
                                .frame(width: UIScreen.main.bounds.width)
                                .environmentObject(chatViewModel.stepFormViewModel)
                        }
                    }
                    .tabViewStyle(PageTabViewStyle(indexDisplayMode: .never))

                    Divider()
                    HStack {
                        if chatViewModel.stepFormViewModel.currentStep > 0 {
                            Button(action: { withAnimation { chatViewModel.stepFormViewModel.currentStep -= 1 } }) {
                                Text("Previous".localized())
                            }
                        }
                        if chatViewModel.stepFormViewModel.currentStep < chatViewModel.stepFormViewModel.stepViewModels.count - 1 {
                            Button(action: { withAnimation { chatViewModel.stepFormViewModel.currentStep += 1 } }) {
                                Text("Next".localized())
                            }
                        }
                        Spacer()
                        Button(action: {
                            chatViewModel.stepFormViewModel.restoreState()
                            completeAction?()
                        }) {
                            Text("取消".localized())
                        }
                        Button(action: {
                            let resumeBlock = {
                                chatViewModel.stepFormViewModel.isProcessing = true
                                chatViewModel.stepFormViewModel.saveToDatabase()

                                // 提取导入的聊天记录
                                let importStep = chatViewModel.stepFormViewModel.stepViewModels.filter { $0.recognizeViewModel.chatMessages.count > 0 }.compactMap { $0 }
                                for step in importStep {
                                    for message in step.recognizeViewModel.chatMessages {
                                        // 只添加不包含的部分
                                        chatViewModel.currentChat.messages.append(contentsOf: message.filter { !chatViewModel.currentChat.messages.contains($0) }.compactMap { $0 })
                                    }
                                }

                                if chatViewModel.stepFormViewModel.isEditing {
                                    // 编辑仅保存
                                    chatListViewModel.newChatTitle = chatViewModel.stepFormViewModel.title
                                    chatListViewModel.renamingChatId = chatViewModel.currentChat.id
                                    chatListViewModel.renameChat()
                                    AdvisorDatabaseManager.shared.update(messages: chatViewModel.currentChat.messages)
                                } else {
                                    // 新建区分本地和云端
                                    // 本地
                                    chatViewModel.saveCurrentChat()

                                    // 确保ChatViewModel不重复添加
                                    if !contentViewModel.chatViewModels.contains(where: { $0.currentChat.id == chatViewModel.currentChat.id }) {
                                        contentViewModel.chatViewModels.append(chatViewModel)
                                    }

                                    chatViewModel.currentModel = ChatViewModel.allModels.first ?? ChatsModel.default

                                    // 确保原子性操作：先保存会话，再更新列表，最后切换会话
                                    Task { @MainActor in
                                        do {
                                            // 1. 确保会话已保存到数据库
                                            AdvisorDatabaseManager.shared.update(chat: chatViewModel.currentChat)

                                            // 2. 立即更新会话列表内存数据，确保实时显示
                                            chatListViewModel.updateMemoryChat(newChat: chatViewModel.currentChat)

                                            // 3. 使用原子化的会话选择方法，确保数据同步
                                            try await contentViewModel.selectChatAtomically(chat: chatViewModel.currentChat, chatListViewModel: chatListViewModel)

                                            logger.info("新会话创建完成并成功切换: chatId=\(chatViewModel.currentChat.id)")

                                        } catch {
                                            logger.error("新会话切换失败: \(error.localizedDescription)")
                                            // 如果切换失败，至少确保会话在列表中可见
                                            contentViewModel.selectedChatID = chatViewModel.currentChat.id
                                        }
                                    }

                                    if chatViewModel.stepFormViewModel.isLocalRecognition {
                                        if chatViewModel.currentChat.messages.filter({ $0.role != .system }).count > 0 {
                                            chatViewModel.postMessages()
                                        }
                                    } else {
                                        if let message = chatViewModel.stepFormViewModel.recognizeViewModel?.constructRequestParameters() {
                                            chatViewModel.postMessages(message)
                                        }
                                    }
                                }
                                completeAction?()
                                chatViewModel.stepFormViewModel.isProcessing = false
                            }
                            if AccountManager.shared.currentUser == nil {
                                withAnimation {
                                    AccountManager.shared.needLoggedIn = true
                                    AccountManager.shared.resumeBlock = resumeBlock
                                }
                                return
                            }
                            resumeBlock()
                        }) {
                            Text("done".localized())
                        }
                        .disabled(chatViewModel.stepFormViewModel.disableComplete)

                        //                    Button(action: {
                        //                         chatViewModel.stepFormViewModel.loadFromDatabase()
                        //                    }) {
                        //                        Text("加载".localized())
                        //                    }
                        //                    Button(action: {
                        //                         chatViewModel.stepFormViewModel.saveToDatabase()
                        //                    }) {
                        //                        Text("保存".localized())
                        //                    }
                    }
                    .padding(AppThemes.padding)
                }
                .frame(width: UIScreen.main.bounds.width)
                .navigationBarTitle(chatViewModel.stepFormViewModel.isEditing ? "\("edit".localized()) \("Context".localized())" : "Context".localized(), displayMode: .inline)
                if chatViewModel.stepFormViewModel.isProcessing {
                    ProgressView()
                        .tint(.mainDark)
                        .ignoresSafeArea()
                }
            }
        }
        .onAppear {
            chatViewModel.stepFormViewModel.backupState()
        }
    }

    private func createStepTextView(index: Int, step: Step) -> some View {
        CustomStepTextView(text: step.stepType.title.localized())
            .onTapGesture {
                withAnimation {
                    chatViewModel.stepFormViewModel.currentStep = index
                }
            }
            .eraseToAnyView()
    }

    private func createIndicator(index: Int, element: Step) -> StepperIndicationType<AnyView> {
        if element.isComplete {
            StepperIndicationType.custom(
                IndicatorImageView(name: "checkmark.circle.fill", isCurrent: chatViewModel.stepFormViewModel.currentStep == index)
                    .foregroundColor(Color.mainDark)
                    .onTapGesture {
                        withAnimation {
                            chatViewModel.stepFormViewModel.currentStep = index
                        }
                    }
                    .eraseToAnyView()
            )
        } else if element.isInProgress {
            StepperIndicationType.custom(
                IndicatorImageView(name: "exclamationmark.circle", isCurrent: chatViewModel.stepFormViewModel.currentStep == index)
                    .foregroundColor(Color.mainDark.opacity(0.5))
                    .onTapGesture {
                        withAnimation {
                            chatViewModel.stepFormViewModel.currentStep = index
                        }
                    }
                    .eraseToAnyView()
            )
        } else {
            StepperIndicationType.custom(
                IndicatorImageView(name: "circle", isCurrent: chatViewModel.stepFormViewModel.currentStep == index)
                    .foregroundColor(Color.gray)
                    .onTapGesture {
                        withAnimation {
                            chatViewModel.stepFormViewModel.currentStep = index
                        }
                    }
                    .eraseToAnyView()
            )
        }
    }

    private func calculateSpacing() -> CGFloat {
        (UIScreen.main.bounds.width - 10.0 * 2.0 - itemWidth * CGFloat(chatViewModel.stepFormViewModel.steps.count)) / CGFloat(chatViewModel.stepFormViewModel.steps.count)
    }
}

struct IndicatorImageView: View {
    var name: String
    var isCurrent: Bool
    let itemWidth: CGFloat = 20

    var body: some View {
        ZStack(alignment: .center) {
            Circle()
                .fill(Color.white)
                .frame(width: itemWidth, height: itemWidth)
                .overlay {
                    if isCurrent {
                        Circle()
                            .stroke(Color.mainDark, lineWidth: 2)
                            .frame(width: itemWidth + 2, height: itemWidth + 2)
                    }
                }
            Image(systemName: name)
                .resizable()
                .frame(width: itemWidth, height: itemWidth)
        }
    }
}

struct CustomStepTextView: View {
    var text: String
    var body: some View {
        VStack {
            Text(text)
                .font(.caption2)
                .foregroundColor(.mainDark)
                .frame(width: 100)
        }
    }
}
