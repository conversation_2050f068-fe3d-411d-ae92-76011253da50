//
//  EmptySessionView.swift
//  ChatAdvisor
//
//  Created by AI Assistant on 2025/01/02.
//

import SwiftUI

/// 空会话状态视图 - 当没有选中会话时显示
struct EmptySessionView: View {
    @State private var animationOffset: CGFloat = 0
    @State private var pulseScale: CGFloat = 1.0
    
    var body: some View {
        VStack(spacing: 30) {
            Spacer()
            
            // 图标动画
            VStack(spacing: 20) {
                Image(systemName: "message.circle")
                    .font(.system(size: 80))
                    .foregroundColor(.accentColor.opacity(0.6))
                    .scaleEffect(pulseScale)
                    .animation(.easeInOut(duration: 2.0).repeatForever(autoreverses: true), value: pulseScale)
                
                Text("开始你的第一次对话")
                    .font(.title2)
                    .fontWeight(.medium)
                    .foregroundColor(.primary)
                
                Text("选择一个会话或创建新的对话")
                    .font(.subheadline)
                    .foregroundColor(.secondary)
                    .multilineTextAlignment(.center)
            }
            
            Spacer()
            
            // 提示信息
            VStack(spacing: 12) {
                HStack(spacing: 12) {
                    Image(systemName: "plus.circle.fill")
                        .foregroundColor(.accentColor)
                    Text("点击左上角的 + 按钮创建新会话")
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
                
                HStack(spacing: 12) {
                    Image(systemName: "list.bullet")
                        .foregroundColor(.accentColor)
                    Text("从左侧菜单选择已有会话")
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
            }
            .padding(.bottom, 50)
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
        .background(Color(UIColor.systemBackground))
        .onAppear {
            pulseScale = 1.1
        }
    }
}

// MARK: - Preview

struct EmptySessionView_Previews: PreviewProvider {
    static var previews: some View {
        EmptySessionView()
    }
}
