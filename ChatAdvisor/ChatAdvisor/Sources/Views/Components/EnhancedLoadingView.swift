//
//  EnhancedLoadingView.swift
//  ChatAdvisor
//
//  Created by AI Assistant on 2025/01/02.
//

import SwiftUI

/// 增强的加载视图 - 提供更好的用户体验
struct EnhancedLoadingView: View {
    let state: LoadingViewState
    let onRetry: (() -> Void)?
    
    @State private var animationOffset: CGFloat = 0
    @State private var pulseScale: CGFloat = 1.0
    
    init(state: LoadingViewState, onRetry: (() -> Void)? = nil) {
        self.state = state
        self.onRetry = onRetry
    }
    
    var body: some View {
        VStack(spacing: 20) {
            switch state {
            case .loading(let message):
                loadingContent(message: message)
            case .skeleton:
                skeletonContent
            case .error(let message):
                errorContent(message: message)
            case .empty(let message):
                emptyContent(message: message)
            case .success:
                EmptyView()
            }
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
        .background(Color(UIColor.systemBackground))
    }
    
    // MARK: - Loading Content
    
    private func loadingContent(message: String) -> some View {
        VStack(spacing: 16) {
            // 动画加载指示器
            ZStack {
                Circle()
                    .stroke(Color.accentColor.opacity(0.3), lineWidth: 4)
                    .frame(width: 50, height: 50)
                
                Circle()
                    .trim(from: 0, to: 0.7)
                    .stroke(Color.accentColor, style: StrokeStyle(lineWidth: 4, lineCap: .round))
                    .frame(width: 50, height: 50)
                    .rotationEffect(.degrees(animationOffset))
                    .animation(.linear(duration: 1).repeatForever(autoreverses: false), value: animationOffset)
            }
            .onAppear {
                animationOffset = 360
            }
            
            Text(message)
                .font(.subheadline)
                .foregroundColor(.secondary)
                .multilineTextAlignment(.center)
                .scaleEffect(pulseScale)
                .animation(.easeInOut(duration: 1).repeatForever(autoreverses: true), value: pulseScale)
                .onAppear {
                    pulseScale = 1.1
                }
        }
        .padding()
    }
    
    // MARK: - Skeleton Content
    
    private var skeletonContent: some View {
        VStack(spacing: 12) {
            ForEach(0..<5, id: \.self) { index in
                HStack {
                    if index % 2 == 0 {
                        Spacer()
                        skeletonBubble(width: 200, alignment: .trailing)
                    } else {
                        skeletonBubble(width: 250, alignment: .leading)
                        Spacer()
                    }
                }
            }
        }
        .padding()
    }
    
    private func skeletonBubble(width: CGFloat, alignment: HorizontalAlignment) -> some View {
        VStack(alignment: alignment, spacing: 4) {
            Rectangle()
                .fill(Color.gray.opacity(0.3))
                .frame(width: width, height: 20)
                .cornerRadius(10)
            
            Rectangle()
                .fill(Color.gray.opacity(0.2))
                .frame(width: width * 0.7, height: 16)
                .cornerRadius(8)
        }
        .shimmer()
    }
    
    // MARK: - Error Content
    
    private func errorContent(message: String) -> some View {
        VStack(spacing: 16) {
            Image(systemName: "exclamationmark.triangle.fill")
                .font(.system(size: 50))
                .foregroundColor(.orange)
            
            Text("出现错误")
                .font(.headline)
                .foregroundColor(.primary)
            
            Text(message)
                .font(.subheadline)
                .foregroundColor(.secondary)
                .multilineTextAlignment(.center)
                .padding(.horizontal)
            
            if let onRetry = onRetry {
                Button(action: onRetry) {
                    HStack {
                        Image(systemName: "arrow.clockwise")
                        Text("重试")
                    }
                    .padding(.horizontal, 20)
                    .padding(.vertical, 10)
                    .background(Color.accentColor)
                    .foregroundColor(.white)
                    .cornerRadius(8)
                }
            }
        }
        .padding()
    }
    
    // MARK: - Empty Content
    
    private func emptyContent(message: String) -> some View {
        VStack(spacing: 16) {
            Image(systemName: "message")
                .font(.system(size: 50))
                .foregroundColor(.gray)
            
            Text("暂无内容")
                .font(.headline)
                .foregroundColor(.primary)
            
            Text(message)
                .font(.subheadline)
                .foregroundColor(.secondary)
                .multilineTextAlignment(.center)
                .padding(.horizontal)
        }
        .padding()
    }
}

// MARK: - Loading View State

enum LoadingViewState {
    case loading(String)
    case skeleton
    case error(String)
    case empty(String)
    case success
}

// MARK: - Shimmer Effect

struct ShimmerEffect: ViewModifier {
    @State private var phase: CGFloat = 0
    
    func body(content: Content) -> some View {
        content
            .overlay(
                Rectangle()
                    .fill(
                        LinearGradient(
                            gradient: Gradient(colors: [
                                Color.clear,
                                Color.white.opacity(0.6),
                                Color.clear
                            ]),
                            startPoint: .leading,
                            endPoint: .trailing
                        )
                    )
                    .rotationEffect(.degrees(30))
                    .offset(x: phase)
                    .animation(.linear(duration: 1.5).repeatForever(autoreverses: false), value: phase)
            )
            .onAppear {
                phase = 300
            }
            .clipped()
    }
}

extension View {
    func shimmer() -> some View {
        modifier(ShimmerEffect())
    }
}

// MARK: - Preview

struct EnhancedLoadingView_Previews: PreviewProvider {
    static var previews: some View {
        Group {
            EnhancedLoadingView(state: .loading("正在加载会话..."))
                .previewDisplayName("Loading")
            
            EnhancedLoadingView(state: .skeleton)
                .previewDisplayName("Skeleton")
            
            EnhancedLoadingView(state: .error("网络连接失败，请检查网络设置")) {
                print("重试")
            }
            .previewDisplayName("Error")
            
            EnhancedLoadingView(state: .empty("还没有任何会话，开始你的第一次对话吧"))
                .previewDisplayName("Empty")
        }
    }
}
