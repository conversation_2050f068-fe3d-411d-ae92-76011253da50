//
//  ScrollPositionReader.swift
//  ChatAdvisor
//
//  Created by AI Assistant on 2025/06/27.
//

import SwiftUI

/// 滚动位置检测器，用于判断用户是否在聊天底部
struct ScrollPositionReader: View {
    let onPositionChange: (Bool) -> Void
    let onDetailedPositionChange: ((CGFloat) -> Void)?
    let threshold: CGFloat

    init(threshold: CGFloat = 100,
         onPositionChange: @escaping (Bool) -> Void,
         onDetailedPositionChange: ((CGFloat) -> Void)? = nil) {
        self.threshold = threshold
        self.onPositionChange = onPositionChange
        self.onDetailedPositionChange = onDetailedPositionChange
    }

    var body: some View {
        GeometryReader { geometry in
            Color.clear
                .preference(key: ScrollOffsetPreferenceKey.self, value: geometry.frame(in: .named("scroll")).minY)
        }
        .frame(height: 0)
        .onPreferenceChange(ScrollOffsetPreferenceKey.self) { value in
            // 当滚动位置接近底部时，认为用户在底部
            let isNearBottom = value >= -threshold
            onPositionChange(isNearBottom)

            // 提供详细的位置信息
            onDetailedPositionChange?(value)
        }
    }
}

/// 滚动偏移量的PreferenceKey
struct ScrollOffsetPreferenceKey: PreferenceKey {
    static var defaultValue: CGFloat = 0
    
    static func reduce(value: inout CGFloat, nextValue: () -> CGFloat) {
        value = nextValue()
    }
}

/// 智能滚动管理器
class SmartScrollManager: ObservableObject {
    @Published var isUserNearBottom: Bool = true
    @Published var isUserNearTop: Bool = false
    @Published var hasNewMessages: Bool = false
    @Published var shouldShowScrollToBottomButton: Bool = false
    @Published var shouldPreloadMessages: Bool = false

    private let bottomThreshold: CGFloat = 100
    private let topThreshold: CGFloat = 200
    private let preloadThreshold: CGFloat = 300

    private var lastScrollPosition: CGFloat = 0
    private var scrollDirection: ScrollDirection = .none

    enum ScrollDirection {
        case up, down, none
    }

    /// 更新用户滚动位置
    func updateUserPosition(isNearBottom: Bool) {
        DispatchQueue.main.async { [weak self] in
            guard let self = self else { return }
            self.isUserNearBottom = isNearBottom
            self.updateScrollToBottomButtonVisibility()
        }
    }

    /// 更新详细的滚动位置信息
    func updateScrollPosition(_ position: CGFloat) {
        DispatchQueue.main.async { [weak self] in
            guard let self = self else { return }

            // 检测滚动方向
            if position > self.lastScrollPosition {
                self.scrollDirection = .down
            } else if position < self.lastScrollPosition {
                self.scrollDirection = .up
            }
            self.lastScrollPosition = position

            // 检查是否接近顶部
            self.isUserNearTop = position <= self.topThreshold

            // 检查是否接近底部
            self.isUserNearBottom = position >= -self.bottomThreshold

            // 检查是否需要预加载
            let shouldPreload = self.scrollDirection == .up && position <= self.preloadThreshold
            if shouldPreload != self.shouldPreloadMessages {
                self.shouldPreloadMessages = shouldPreload
            }

            self.updateScrollToBottomButtonVisibility()
        }
    }

    /// 标记有新消息
    func markNewMessage() {
        DispatchQueue.main.async { [weak self] in
            guard let self = self else { return }
            self.hasNewMessages = true
            self.updateScrollToBottomButtonVisibility()
        }
    }

    /// 清除新消息标记
    func clearNewMessages() {
        DispatchQueue.main.async { [weak self] in
            guard let self = self else { return }
            self.hasNewMessages = false
            self.updateScrollToBottomButtonVisibility()
        }
    }

    /// 判断是否应该自动滚动
    var shouldAutoScroll: Bool {
        return isUserNearBottom
    }

    /// 判断是否应该触发预加载
    var shouldTriggerPreload: Bool {
        return shouldPreloadMessages && scrollDirection == .up
    }

    /// 重置滚动状态
    func resetScrollState() {
        DispatchQueue.main.async { [weak self] in
            guard let self = self else { return }
            self.isUserNearBottom = true
            self.isUserNearTop = false
            self.hasNewMessages = false
            self.shouldPreloadMessages = false
            self.shouldShowScrollToBottomButton = false
            self.lastScrollPosition = 0
            self.scrollDirection = .none
        }
    }

    // MARK: - 滚动位置保持

    private var savedScrollPositions: [String: ScrollState] = [:]

    struct ScrollState {
        let position: CGFloat
        let messageId: String?
        let timestamp: Date
    }

    /// 保存当前滚动位置
    func saveScrollPosition(for chatId: String, position: CGFloat, messageId: String?) {
        let scrollState = ScrollState(
            position: position,
            messageId: messageId,
            timestamp: Date()
        )
        savedScrollPositions[chatId] = scrollState
    }

    /// 恢复滚动位置
    func restoreScrollPosition(for chatId: String) -> ScrollState? {
        return savedScrollPositions[chatId]
    }

    /// 清理过期的滚动位置
    func cleanupOldScrollPositions() {
        let cutoffDate = Date().addingTimeInterval(-3600) // 1小时前
        savedScrollPositions = savedScrollPositions.filter { _, state in
            state.timestamp > cutoffDate
        }
    }

    /// 更新滚动到底部按钮的可见性
    private func updateScrollToBottomButtonVisibility() {
        shouldShowScrollToBottomButton = !isUserNearBottom && hasNewMessages
    }
}

/// 滚动到底部按钮
struct ScrollToBottomButton: View {
    let action: () -> Void
    @State private var isVisible: Bool = false
    
    var body: some View {
        Button(action: action) {
            HStack(spacing: 4) {
                Image(systemName: "arrow.down")
                    .font(.system(size: 12, weight: .medium))
                Text("回到底部")
                    .font(.system(size: 12, weight: .medium))
            }
            .foregroundColor(.white)
            .padding(.horizontal, 12)
            .padding(.vertical, 6)
            .background(Color.mainDark.opacity(0.8))
            .cornerRadius(16)
            .shadow(color: .black.opacity(0.1), radius: 2, x: 0, y: 1)
        }
        .scaleEffect(isVisible ? 1.0 : 0.8)
        .opacity(isVisible ? 1.0 : 0.0)
        .animation(.spring(response: 0.3, dampingFraction: 0.7), value: isVisible)
        .onAppear {
            withAnimation(.easeOut(duration: 0.2)) {
                isVisible = true
            }
        }
        .onDisappear {
            isVisible = false
        }
    }
}
