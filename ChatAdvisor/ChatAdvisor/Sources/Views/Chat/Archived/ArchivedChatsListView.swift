//
//  ArchivedChatsListView.swift
//  JunShi
//
//  Created by md on 2024/5/6.
//

import Foundation
import SwifterSwift
import SwiftUI

struct ArchivedChatsListView: View {
    @FocusState private var isTextFieldFocused: Bool
    @ObservedObject var viewModel: ArchivedChatViewModel

    private let shouldExpand = false

    var body: some View {
        VStack {
            ZStack(alignment: .trailing) {
                TextField("搜索聊天".localized(), text: $viewModel.searchText)
                    .padding(7)
                    .background(Color(UIColor(light: .systemGray6, dark: .systemGray6)))
                    .cornerRadius(8)
                    .onChange(of: viewModel.searchText) { newValue in
                        if newValue.isEmpty {
                            viewModel.refreshChats()
                        } else {
                            viewModel.searchChats()
                        }
                    }
                    .focused($isTextFieldFocused)
                    .onChange(of: isTextFieldFocused) { isFocused in
                        if isFocused == false {
                            viewModel.refreshChats()
                        }
                    }
                isTextFieldFocused ? Button(action: {
                    withAnimation {
                        isTextFieldFocused = false
                    }
                }) {
                    withAnimation {
                        Image(systemName: "xmark")
                            .foregroundColor(.mainDark)
                            .frame(width: 44, height: 44)
                    }
                } : nil
            }
            ScrollView {
                LazyVStack {
                    // 使用 forEach 遍历分组
                    ForEach(Array(viewModel.groupedChats.keys.sorted(by: >)), id: \.self) { date in
                        Section {
                            ForEach(viewModel.groupedChats[date] ?? [], id: \.id) { chat in
                                Button(action: {
                                    viewModel.setCurrentChat(chat: chat)
                                }) {
                                    Text(chat.title != "" ? chat.title : "没有标题的会话".localized())
                                        .font(.subheadline)
                                        .foregroundColor(.primary)
                                        .frame(maxWidth: .infinity, alignment: .leading)
                                }
                                .listRowInsets(EdgeInsets())
                                .listRowSeparator(.hidden)
                                .contentShape(Rectangle())
                                .padding(.vertical, 6)
                                .padding(.horizontal, 12)
                                .frame(height: 44)
                                .background(chat.id == viewModel.currentChat.id ? Color(.systemGray5) : Color.clear)
                                .iOS16(chat: chat, viewModel: viewModel)
                            }
                            .listRowSeparator(.automatic)
                        } header: {
                            Text(date.chatDateLabel())
                                .font(.headline)
                                .foregroundColor(.mainDark)
                        }
                    }
                    .ignoresSafeArea()
                    if !viewModel.isLoadFinished {
                        ProgressView()
                            .tint(.mainDark)
                            .frame(maxWidth: .infinity, maxHeight: .infinity)
                            .onAppear {
                                viewModel.fetchMoreChat()
                            }
                    }
                }
            }
            .onAppear {
                viewModel.fetchMoreChat()
            }
        }
        .alert("重命名".localized(), isPresented: $viewModel.isRenaming) {
            TextField(viewModel.newChatTitle, text: $viewModel.newChatTitle)
            Button("取消".localized(), role: .cancel) {
                viewModel.isRenaming = false
            }
            Button("确认".localized()) {
                viewModel.renameChat()
            }
        } message: {
            Text("请输入新的聊天标题".localized())
        }
    }
}

private extension View {
    @ViewBuilder
    func iOS16(chat: Chat, viewModel: ArchivedChatViewModel) -> some View {
        if #available(iOS 16.0, *) {
            self.contextMenu {
                Button(action: {
                    viewModel.isRenaming = true
                    viewModel.newChatTitle = viewModel.getChatTitle(id: chat.id)
                    viewModel.renamingChatId = chat.id
                }) {
                    Text("重命名".localized())
                    Image(systemName: "pencil")
                }
                Button(action: {
                    viewModel.unarchiveChat(id: chat.id)
                }) {
                    Text("取消归档".localized())
                    Image(systemName: "archivebox")
                }
                Button(action: {
                    viewModel.deleteChat(id: chat.id)
                }) {
                    Text("删除".localized())
                    Image(systemName: "trash")
                }
            } preview: {
                ChatViewPreview(chat: chat)
                    .frame(width: UIScreen.main.bounds.width - 40, height: UIScreen.main.bounds.height * 0.65)
                    .cornerRadius(10)
                    .padding()
            }
        } else {
            contextMenu(ContextMenu(menuItems: {
                Button(action: {
                    viewModel.isRenaming = true
                    viewModel.newChatTitle = viewModel.getChatTitle(id: chat.id)
                    viewModel.renamingChatId = chat.id
                }) {
                    Text("重命名".localized())
                    Image(systemName: "pencil")
                }
                Button(action: {
                    viewModel.unarchiveChat(id: chat.id)
                }) {
                    Text("归档".localized())
                    Image(systemName: "archivebox")
                }
                Button(action: {
                    viewModel.deleteChat(id: chat.id)
                }) {
                    Text("删除".localized())
                    Image(systemName: "trash")
                }
            }))
        }
    }
}
