// Romanian
"empty_session" = "Nici o conversație momentan";
"个人" = "Personal";
"以确认删除" = "Pentru a confirma ștergerea";
"价格" = "Preț";
"余额" = "Sold";
"充值" = "Reîncarcă";
"充值结果" = "Rezultatul reîncărcării";
"充值金额" = "Suma reîncărcată";
"删除" = "Șterge";
"删除确认" = "Confirmarea ștergerii";
"删除账号" = "Șterge contul";
"发送" = "Trimite";
"发送邮件" = "Trimite e-mail";
"取消归档" = "Anulează arhivarea";
"口吻" = "Ton";
"复制" = "Copiază";
"好的" = "OK";
"字" = "Caracter";
"密码" = "Parolă";
"已复制" = "Copiat";
"已归档" = "Arhivat";
"开启后会话将模仿口吻回复" = "După activare, conversația va imita tonul răspunsului";
"开始新的对话吧" = "Începe o nouă conversație";
"归档" = "Arhivează";
"当前余额:" = "Soldul curent:";
"您确定要删除账号吗？" = "Ești sigur că vrei să ștergi contul?";
"意见与建议" = "Feedback și sugestii";
"我确定删除账号" = "Confirm ștergerea contului";
"搜索聊天" = "Caută conversație";
"暂无归档" = "Nici o arhivă";
"服务条款" = "Termeni de serviciu";
"朗读" = "Citește cu voce tare";
"条款" = "Termeni";
"每" = "Pe";
"没有标题的会话" = "Fără titlu";
"注册" = "Înregistrează-te";
"版本" = "Versiune";
"用户" = "Utilizator";
"登出" = "Deconectare";
"登录" = "Conectare";
"确定" = "Confirmă";
"确认删除输入错误, 请重新输入" = "Eroare de confirmare a ștergerii, vă rugăm să reintroduceți";
"秒后重发" = "secunde pentru retrimitere";
"约" = "Despre";
"设置" = "Setări";
"请输入" = "Vă rugăm să introduceți";
"请输入你的邮箱地址和密码" = "Vă rugăm să introduceți adresa de e-mail și parola";
"请输入你的邮箱地址，以及大于6位数的密码" = "Vă rugăm să introduceți adresa de e-mail și o parolă mai mare de 6 caractere";
"请输入新的聊天标题" = "Vă rugăm să introduceți un nou titlu pentru conversație";
"账号删除后无法恢复" = "Ștergerea contului va șterge toate datele și nu poate fi recuperată";
"输入:" = "Intrare:";
"输出:" = "Ieșire:";
"邮箱" = "E-mail";
"邮箱登录" = "Conectare prin e-mail";
"重命名" = "Redenumește";
"隐私政策" = "Politica de confidențialitate";
"震动反馈" = "Feedback de vibrație";
"验证" = "Verificare";
"昨天" = "Ieri";
"今天" = "Astăzi";
"年" = "An";
"月" = "Lună";
"日" = "Zi";
"收费标准" = "Standard de taxare";
"取消"="Anulează";
"服务器内部错误"="Eroare internă a serverului";
"当前设备没有开启应用内购买功能"="Dispozitivul curent nu are activată achiziția în aplicație";
"获取商品失败"="Eșec la obținerea produselor";
"充值说明"="Sfaturi";
"购买失败"="Achiziție eșuată";
"重试"="Reîncearcă";
"完整会话"="Sesiune completă";
"开启可以让对话回答更精准,可能会加快余额消耗"="Activarea poate face răspunsurile mai precise, ceea ce poate accelera consumul soldului";
"语音"="Voce";
"语音识别"="Selectați limba pentru recunoașterea vocală pentru a îmbunătăți acuratețea";
"选择语言"="Selectați limba";
"通过Google登录"="Conectare cu Google";
"通过Facebook登录"="Conectare cu Facebook";
"通过Twitter登录"="Conectare cu Twitter";
"主色调"="Culoare principală";
"帮你聊天" = "Lăsați cel mai avansat AI să vă ajute să discutați";
"继续使用" = "Continuând să utilizați, sunteți de acord cu termenii noștri";
"Apple登录" = "Conectare cu Apple";

// chatadvisor
"done" = "Ferdig";
"Previous" = "Forrige";
"Next" = "Neste";
"History" = "Historikk";
"Context" = "Kontekst";
"Become friends" = "Bli venner";
"Choose Please" = "Vennligst velg";
// Relationships
"Friend" = "Venn";
"Family" = "Familie";
"Colleague" = "Kollega";
"Stranger" = "Fremmed";
"Neighbor" = "Nabo";
"Classmate" = "Klassekamerat";
"Teammate" = "Lagkamerat";
"Acquaintance" = "Kjent";
"Mentor" = "Mentor";
"Mentee" = "Lærling";
"Business Partner" = "Forretningspartner";
"Romantic Partner" = "Romantisk partner";
"Spouse" = "Ektefelle";
"Ex-Partner" = "Ekspartner";
"Sibling" = "Søsken";
"Parent" = "Forelder";
"Child" = "Barn";
"Grandparent" = "Besteforelder";
"Grandchild" = "Barnebarn";
"In-law" = "Sviger";
"Coworker" = "Medarbeider";
"Boss" = "Sjef";
"Subordinate" = "Underordnet";
"Client" = "Kunde";
"Tutor" = "Lærer";
"Roommate" = "Romkamerat";
"Lab Partner" = "Lab-partner";
"Study Buddy" = "Studiekamerat";
"Project Teammate" = "Prosjektkollega";
"Online Friend" = "Nettvenn";
"Pen Pal" = "Brevvenn";
"Travel Companion" = "Reisekamerat";
"Sports Teammate" = "Idrettslagkamerat";
"Doctor" = "Lege";
"Patient" = "Pasient";
"Customer Service" = "Kundeservice";
"Supplier" = "Leverandør";
"Landlord" = "Utleier";
"Tenant" = "Leietaker";
"Business Competitor" = "Forretningskonkurrent";
// Degrees
"Close" = "Nær";
"Casual" = "Uformell";
"Distant" = "Fjern";
"Professional" = "Profesjonell";
"Complicated" = "Komplisert";
"Trusted" = "Tillitsfull";
"Intimate" = "Intim";
// Styles
"Formal" = "Formell";
"Casual" = "Uformell";
"Friendly" = "Vennlig";
"Professional" = "Profesjonell";
"Humorous" = "Humoristisk";
"Direct" = "Direkte";
"Empathetic" = "Empatisk";
"Supportive" = "Støttende";
"Inquisitive" = "Undrende";
// Response Lengths
"Short" = "Kort";
"Medium" = "Medium";
"Long" = "Lang";
"Detailed" = "Detaljert";
// Forms
"Information" = "Informasjon";
"Preferences" = "Preferanser";
"Emotion" = "Følelse";
"Custom" = "Tilpasset";
"Chat_history" = "Importer Chat-historikk";
"Your Gender" = "Ditt kjønn";
"Your Age" = "Din alder";
"Chat Partner's Gender" = "Samtalepartnerens kjønn";
"Chat Partner's Age" = "Samtalepartnerens alder";
"Known Since" = "Kjent siden";
"Topics" = "Emner";
"Goal" = "Mål";
"Chat Partner's Emotion" = "Samtalepartnerens følelse";
"Preferred Emotion in Responses" = "Foretrukket følelse i svar";
"Preferred_Chat_Style" = "Foretrukket chatte-stil";
"Preferred_Response_Length" = "Foretrukket svart lengde";
"Relationship_Type" = "Forholdstype";
"Relationship_Status" = "Forholdsstatus";
"form_help" = "
### Raskt Steg Bytte
- Dra til venstre eller høyre på stegene ovenfor eller klikk på dem for å bytte steg raskt.
### Valgfritt Innhold
- Alt innhold er valgfritt.
- Jo mer detaljert inndata, jo mer nøyaktige AI-svar.
### Importer Chat-historikk
- Klikk på knappen nedenfor for å begynne å importere chat-skjermbilder.";
"promot_cloud"="Voi oferi un context sau un obiectiv și sper că poți genera un răspuns adecvat bazat pe aceste informații și în tonul și perspectiva mea. Dacă sunt stabilite obiective, ajută-mă să le ating cât mai repede posibil. Trebuie doar să furnizezi conținutul răspunsului.";
"promot_local" = "Acum joci rolul utilizatorului, în timp ce utilizatorul va juca rolul persoanei cu care discută. Asigură-te că răspunsurile tale sunt conforme cu scenariul stabilit de utilizator și menține coerența și naturalețea. Dacă sunt stabilite obiective, ajută utilizatorul să le atingă cât mai repede posibil. Reține că înregistrările de chat sunt texte recunoscute din imagini, ceea ce poate duce la neconcordanțe.";
"Import_local" = "Recunoaștere locală";
"Import_remote" = "Recunoaștere cloud";
"Relationship Background" = "Forholds bakgrunn";
//"chat_help"="点击标题编辑情境信息,点击新建配置新的情境";
"chat_help"="Klikk på tittelen for å redigere kontekstinformasjonen, klikk på Ny for å konfigurere ny kontekst";
"edit" = "Rediger";
"Me"="Meg";
"对方"="Mål";
"使用"="Bruk";
"Guest"="Gjest";
"收到的的验证码"="Mottatt verifiseringskode";
"允许广告" = "Permiteți anunțuri";
"广告提示" = "Vă rugăm să permiteți anunțurile pentru a sprijini dezvoltarea noastră mai bună. Acesta va fi resetat de fiecare dată când reporniți. Puteți dezactiva permanent după reîncărcare.";
"Other Apps" = "Alte aplicații";
"打开" = "Deschide";
"下载" = "Descarcă";
"其他应用" = "Alte aplicații";
"关于" = "Despre";
"chat4o" = "Chat4o";
"chat4o_description" = "Un chat pentru toți, asistentul AI suprem! Ajută la rezolvarea diferitelor probleme din viață, muncă, studii și emoții prin conversații inteligente. Fie că este vorba despre sarcini zilnice sau provocări complexe, oferă soluții eficiente și personalizate.";
"textGame" = "Story Maker";
"textGame_description" = "Un joc interactiv bazat pe texte în care poți alege liber direcția poveștii și îți lași imaginația să zboare!";
"chatAdvisor" = "Chat Advisor";
"chatAdvisor_description" = "Lasă AI-ul avansat să genereze răspunsuri la conversații pentru tine.";
"通过Tiktok登录" = "Conectează-te cu Tiktok";
