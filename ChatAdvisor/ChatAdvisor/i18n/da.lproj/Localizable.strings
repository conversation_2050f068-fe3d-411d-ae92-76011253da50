// Danish
"empty_session" = "Ingen samtale i øjeblikket";
"个人" = "Personlig";
"以确认删除" = "For at bekræfte sletning";
"价格" = "Pris";
"余额" = "Saldo";
"充值" = "Genoplad";
"充值结果" = "Genopladningsresultat";
"充值金额" = "Beløb til genopladning";
"删除" = "Slet";
"删除确认" = "Bekræftelse på sletning";
"删除账号" = "Slet konto";
"发送" = "Send";
"发送邮件" = "Send e-mail";
"取消归档" = "Annuller arkivering";
"口吻" = "Tone";
"复制" = "Kopiér";
"好的" = "OK";
"字" = "Tegn";
"密码" = "Adgangskode";
"已复制" = "Kopieret";
"已归档" = "Arkiveret";
"开启后会话将模仿口吻回复" = "Når aktiveret, vil samtalen efterligne tonen i svaret";
"开始新的对话吧" = "Start en ny samtale";
"归档" = "Arkiv";
"当前余额:" = "Aktuel saldo:";
"您确定要删除账号吗？" = "Er du sikker på, at du vil slette din konto?";
"意见与建议" = "Feedback og forslag";
"我确定删除账号" = "Jeg bekræfter sletning af konto";
"搜索聊天" = "Søg chat";
"暂无归档" = "Ingen arkiver";
"服务条款" = "Servicevilkår";
"朗读" = "Læs højt";
"条款" = "Vilkår";
"每" = "Per";
"没有标题的会话" = "Ingen titel";
"注册" = "Tilmeld dig";
"版本" = "Version";
"用户" = "Bruger";
"登出" = "Log ud";
"登录" = "Log ind";
"确定" = "Bekræft";
"确认删除输入错误, 请重新输入" = "Bekræftelse af sletning fejlindtastning, prøv igen";
"秒后重发" = "sekunder til genudsendelse";
"约" = "Om";
"设置" = "Indstillinger";
"请输入" = "Indtast venligst";
"请输入你的邮箱地址和密码" = "Indtast din e-mailadresse og adgangskode";
"请输入你的邮箱地址，以及大于6位数的密码" = "Indtast din e-mailadresse og en adgangskode på mere end 6 tegn";
"请输入新的聊天标题" = "Indtast venligst en ny chat-titel";
"账号删除后无法恢复" = "Konto sletning vil fjerne alle data og kan ikke gendannes";
"输入:" = "Indtast:";
"输出:" = "Output:";
"邮箱" = "E-mail";
"邮箱登录" = "E-mail login";
"重命名" = "Omdøb";
"隐私政策" = "Privatlivspolitik";
"震动反馈" = "Vibrationsfeedback";
"验证" = "Verifikation";
"昨天" = "I går";
"今天" = "I dag";
"年" = "År";
"月" = "Måned";
"日" = "Dag";
"收费标准" = "Takst";
"取消"="Annuller";
"服务器内部错误"="Intern serverfejl";
"当前设备没有开启应用内购买功能"="Den nuværende enhed har ikke aktiveret indkøb i appen";
"获取商品失败"="Fejl ved hentning af varer";
"充值说明"="Genopladningsinstruktioner";
"购买失败"="Køb mislykkedes";
"重试"="Prøv igen";
"完整会话"="Fuld session";
"开启可以让对话回答更精准,可能会加快余额消耗"="Aktivering kan få samtalen til at svare mere præcist, hvilket kan øge saldoen hurtigere";
"语音"="Stemme";
"语音识别"="Vælg sprog til stemmegenkendelse for at forbedre nøjagtigheden";
"选择语言"="Vælg sprog";
"通过Google登录"="Log ind med Google";
"通过Facebook登录"="Log ind med Facebook";
"通过Twitter登录"="Log ind med Twitter";
"主色调"="Primær farve";
"Apple登录" = "Log ind med Apple";

"done"="Udført";
"Previous"="Forrige";
"Next"="Næste";
"History"="Historik";
"Context"="Kontekst";
"Become friends"="Bliv venner";
"Choose Please"="Vælg venligst";
// Relationships
"Friend"="Ven";
"Family"="Familie";
"Colleague"="Kollega";
"Stranger"="Fremmed";
"Neighbor"="Nabo";
"Classmate"="Klasskammerat";
"Teammate"="Holdkammerat";
"Acquaintance"="Bekendt";
"Mentor"="Mentor";
"Mentee"="Mentee";
"Business Partner"="Forretningspartner";
"Romantic Partner"="Romantisk partner";
"Spouse"="Ægtefælle";
"Ex-Partner"="Eks-partner";
"Sibling"="Søskende";
"Parent"="Forælder";
"Child"="Barn";
"Grandparent"="Bedsteforælder";
"Grandchild"="Barnebarn";
"In-law"="Svigerforælder";
"Coworker"="Arbejdskollega";
"Boss"="Chef";
"Subordinate"="Underordnet";
"Client"="Klient";
"Tutor"="Tutor";
"Roommate"="Værelseskammerat";
"Lab Partner"="Laboratoriepartner";
"Study Buddy"="Studiekammerat";
"Project Teammate"="Projektpartner";
"Online Friend"="Online ven";
"Pen Pal"="Brevven";
"Travel Companion"="Rejseledsager";
"Sports Teammate"="Sportsmakker";
"Doctor"="Læge";
"Patient"="Patient";
"Customer Service"="Kundeservice";
"Supplier"="Leverandør";
"Landlord"="Udlejer";
"Tenant"="Lejer";
"Business Competitor"="Forretningskonkurrent";
// Degrees
"Close"="Nær";
"Casual"="Uformel";
"Distant"="Fjern";
"Professional"="Professionel";
"Complicated"="Kompleks";
"Trusted"="Betroet";
"Intimate"="Intim";
// Styles
"Formal"="Formel";
"Casual"="Uformel";
"Friendly"="Venlig";
"Professional"="Professionel";
"Humorous"="Humoristisk";
"Direct"="Direkte";
"Empathetic"="Empatisk";
"Supportive"="Støttende";
"Inquisitive"="Nysgerrig";
// Response Lengths
"Short"="Kort";
"Medium"="Mellem";
"Long"="Lang";
"Detailed"="Detaljeret";
// Forms
"Information"="Information";
"Preferences"="Præferencer";
"Emotion"="Følelse";
"Custom"="Brugerdefineret";
"Chat_history"="Importer chat historie";
"Your Gender"="Dit køn";
"Your Age"="Din alder";
"Chat Partner's Gender"="Chatpartners køn";
"Chat Partner's Age"="Chatpartners alder";
"Known Since"="Kendt siden";
"Topics"="Emner";
"Goal"="Mål";
"Chat Partner's Emotion"="Chatpartners følelse";
"Preferred Emotion in Responses"="Foretrukken følelse i svar";
"Preferred_Chat_Style"="Foretrukken chat stil";
"Preferred_Response_Length"="Foretrukken svar længde";
"Relationship_Type"="Relationstype";
"Relationship_Status"="Relationsstatus";
"form_help"="
### Hurtig trin skift
- Stryg til venstre eller højre eller klik på trinene ovenfor for hurtigt at skifte trin.
### Valgfrit indhold
- Alt indhold er valgfrit.
- Jo mere detaljeret input, desto mere præcise AI svar.
### Importer chat historie
- Klik på knappen nedenfor for at begynde at importere chat skærmbilleder.
";
"promot_cloud"="Jeg vil give en kontekst eller mål, og jeg håber, at du kan generere et passende svar baseret på disse oplysninger og i min tone og perspektiv. Hvis mål er sat, hjælp mig med at nå dem så hurtigt som muligt. Du behøver kun at give indholdet af svaret.";
"promot_local"="Du spiller nu rollen som brugeren, mens brugeren vil spille rollen som personen, de chatter med. Sørg for, at dine svar er i overensstemmelse med det scenario, som brugeren har sat, og oprethold sammenhæng og naturlighed. Hvis målene er sat, så hjælp brugeren med at opnå dem så hurtigt som muligt. Vær opmærksom på, at chatteksterne er tekstgenkendt fra billeder, hvilket kan resultere i misalignment.";
"Import_local" = "Local Recognition";
"Import_remote" = "Cloud Recognition";
"Relationship Background"="Relationsbaggrund";
//"chat_help"="点击标题编辑情境信息,点击新建配置新的情境";
"chat_help"="Klik på titlen for at redigere kontekstinformation, klik på Opret ny for at konfigurere en ny kontekst";
"edit" = "Rediger";
"Me"="Mig";
"对方"="Mål";
"使用"="Brug";
"Guest"="Gæst";
"收到的的验证码"="Modtaget kode";
"允许广告" = "Tillad annoncer";
"广告提示" = "Venligst tillad annoncer for at støtte vores bedre udvikling. Dette vil blive nulstillet hver gang du genstarter. Du kan permanent deaktivere det efter at have genopladet.";
"Other Apps" = "Andre apps";
"打开" = "Åbn";
"下载" = "Download";
"其他应用" = "Andre apps";
"关于" = "Om";
"chat4o" = "Chat4o";
"chat4o_description" = "En chat for alle, den ultimative AI-assistent! Hjælper med at løse forskellige livs-, arbejds-, studie- og følelsesmæssige problemer gennem intelligent samtale. Uanset om det drejer sig om daglige opgaver eller komplekse udfordringer, tilbyder det effektive og personaliserede løsninger.";
"textGame" = "Story Maker";
"textGame_description" = "Et interaktivt tekstbaseret spil, hvor du frit vælger handlingens retning og lader din fantasi løbe løbsk!";
"chatAdvisor" = "Chat Advisor";
"chatAdvisor_description" = "Lad avanceret AI generere chat-svar for dig.";
"通过Tiktok登录" = "Log ind med Tiktok";
"帮你聊天" = "Lad den mest avancerede AI hjælpe dig med at chatte";
"继续使用" = "Ved at fortsætte med at bruge, accepterer du vores";
