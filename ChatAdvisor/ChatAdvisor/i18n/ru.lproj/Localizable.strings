/* 
  Localizable.strings
  JunShi

  Created by zweiteng on 2024/6/2.
  
*/

// Russian (ru)
"empty_session" = "Нет беседы в данный момент";
"个人" = "Личное";
"以确认删除" = "Для подтверждения удаления";
"价格" = "Цена";
"余额" = "Баланс";
"充值" = "Пополнить";
"充值结果" = "Результат пополнения";
"充值金额" = "Сумма пополнения";
"删除" = "Удалить";
"删除确认" = "Подтверждение удаления";
"删除账号" = "Удалить учетную запись";
"发送" = "Отправить";
"发送邮件" = "Отправить по электронной почте";
"取消归档" = "Отменить архивирование";
"口吻" = "Интонация";
"复制" = "Копировать";
"好的" = "Хорошо";
"字" = "Слово";
"密码" = "Пароль";
"已复制" = "Скопировано";
"已归档" = "Архивировано";
"开启后会话将模仿口吻回复" = "После включения сеанса будет имитироваться интонация в ответах";
"开始新的对话吧" = "Начните новый разговор";
"归档" = "Архив";
"当前余额:" = "Текущий баланс:";
"您确定要删除账号吗？" = "Вы уверены, что хотите удалить учетную запись?";
"意见与建议" = "Отзывы и предложения";
"我确定删除账号" = "Я уверен, что хочу удалить учетную запись";
"搜索聊天" = "Поиск разговора";
"暂无归档" = "Нет архива";
"服务条款" = "Условия обслуживания";
"朗读" = "Чтение вслух";
"条款" = "Условия";
"每" = "Каждый";
"注册" = "Регистрация";
"版本" = "Версия";
"用户" = "Пользователь";
"登出" = "Выход";
"登录" = "Войти";
"确定" = "Подтвердить";
"确认删除输入错误, 请重新输入" = "Подтверждение на удаление введено неверно, повторите ввод";
"秒后重发" = "секунд до повторной отправки";
"约" = "Примерно";
"设置" = "Настройки";
"请输入" = "Пожалуйста, введите";
"请输入你的邮箱地址和密码" = "Пожалуйста, введите свой адрес электронной почты и пароль";
"请输入你的邮箱地址，以及大于6位数的密码" = "Пожалуйста, введите свой адрес электронной почты и пароль длиной более 6 символов";
"请输入新的聊天标题" = "Пожалуйста, введите новый заголовок беседы";
"账号删除后无法恢复" = "Удаление учетной записи приведет к удалению всех данных и не может быть восстановлено";
"输入:" = "Ввод:";
"输出:" = "Вывод:";
"邮箱" = "Электронная почта";
"邮箱登录" = "Вход по электронной почте";
"重命名" = "Переименовать";
"隐私政策" = "Политика конфиденциальности";
"震动反馈" = "Вибрационная обратная связь";
"验证" = "Подтверждение";
"没有标题的会话" = "Нет заголовка";
"昨天" = "Вчера";
"今天" = "Сегодня";
"年" = "Год";
"月" = "Месяц";
"日" = "День";
"收费标准" = "Тарифы";
"取消"="Отмена";
"服务器内部错误"="Внутренняя ошибка сервера";
"当前设备没有开启应用内购买功能"="На текущем устройстве не включена функция покупок в приложении";
"获取商品失败"="Не удалось получить товар";
"充值说明"="Инструкция по пополнению";
"购买失败"="Покупка не удалась";
"重试"="Повторить";
"完整会话"="Полный разговор";
"开启可以让对话回答更精准,可能会加快余额消耗"="Включение позволит более точно отвечать на вопросы в разговоре, что может ускорить расход баланса";
"语音"="Голос";
"语音识别"="Выберите язык распознавания речи для повышения точности";
"选择语言"="Выберите язык";
"通过Google登录"="Войти через Google";
"通过Facebook登录"="Войти через Facebook";
"通过Twitter登录"="Войти через Twitter";
"主色调"="Основной цвет";
"Apple登录" = "Войти через Apple";
"done"="Готово";
"Previous" = "Предыдущий";
"Next" = "Следующий";
"History" = "История";
"Context" = "Контекст";
"Become friends" = "Становиться друзьями";
"Choose Please" = "Пожалуйста, выберите";
// Relationships
"Friend" = "Друг";
"Family" = "Семья";
"Colleague" = "Коллега";
"Stranger" = "Незнакомец";
"Neighbor" = "Сосед";
"Classmate" = "Одноклассник";
"Teammate" = "Товарищ по команде";
"Acquaintance" = "Знакомый";
"Mentor" = "Наставник";
"Mentee" = "Подопечный";
"Business Partner" = "Деловой партнер";
"Romantic Partner" = "Романтический партнер";
"Spouse" = "Супруг/супруга";
"Ex-Partner" = "Бывший партнер";
"Sibling" = "Брат/сестра";
"Parent" = "Родитель";
"Child" = "Ребенок";
"Grandparent" = "Бабушка/дедушка";
"Grandchild" = "Внук/внучка";
"In-law" = "Родственник по браку";
"Coworker" = "Коллега";
"Boss" = "Начальник";
"Subordinate" = "Подчиненный";
"Client" = "Клиент";
"Tutor" = "Репетитор";
"Roommate" = "Сосед по комнате";
"Lab Partner" = "Партнер по лаборатории";
"Study Buddy" = "Товарищ по учебе";
"Project Teammate" = "Партнер по проекту";
"Online Friend" = "Друг в интернете";
"Pen Pal" = "Друг по переписке";
"Travel Companion" = "Попутчик";
"Sports Teammate" = "Спортивный товарищ";
"Doctor" = "Врач";
"Patient" = "Пациент";
"Customer Service" = "Служба поддержки клиентов";
"Supplier" = "Поставщик";
"Landlord" = "Арендодатель";
"Tenant" = "Арендатор";
"Business Competitor" = "Деловой конкурент";
// Degrees
"Close" = "Близкий";
"Casual" = "Неофициальный";
"Distant" = "Далекий";
"Professional" = "Профессиональный";
"Complicated" = "Сложный";
"Trusted" = "Доверенный";
"Intimate" = "Интимный";
// Styles
"Formal" = "Формальный";
"Casual" = "Неофициальный";
"Friendly" = "Дружелюбный";
"Professional" = "Профессиональный";
"Humorous" = "Юмористический";
"Direct" = "Прямой";
"Empathetic" = "Сочувствующий";
"Supportive" = "Поддерживающий";
"Inquisitive" = "Любознательный";
// Response Lengths
"Short" = "Короткий";
"Medium" = "Средний";
"Long" = "Длинный";
"Detailed" = "Подробный";
// Forms
"Information" = "Информация";
"Preferences" = "Предпочтения";
"Emotion" = "Эмоция";
"Custom" = "Настройка";
"Chat_history" = "Импортировать историю чата";
"Your Gender" = "Ваш пол";
"Your Age" = "Ваш возраст";
"Chat Partner's Gender" = "Пол собеседника";
"Chat Partner's Age" = "Возраст собеседника";
"Known Since" = "Знакомы с";
"Topics" = "Темы";
"Goal" = "Цель";
"Chat Partner's Emotion" = "Эмоция собеседника";
"Preferred Emotion in Responses" = "Предпочитаемая эмоция в ответах";
"Preferred_Chat_Style" = "Предпочитаемый стиль чата";
"Preferred_Response_Length" = "Предпочитаемая длина ответа";
"Relationship_Type" = "Тип отношений";
"Relationship_Status" = "Статус отношений";
"form_help"="
### Быстрое переключение шагов
- Проведите влево или вправо или нажмите на шаги выше для быстрого переключения шагов.
### Необязательный контент
- Весь контент является необязательным.
- Чем больше введено информации, тем точнее ответы ИИ.
### Импорт истории чата
- Нажмите кнопку ниже, чтобы начать импорт снимков экрана чата.
";
"promot_cloud"="Я предоставлю контекст или цель, и надеюсь, что вы сможете создать соответствующий ответ, основываясь на этой информации и в моем тоне и перспективе. Если цели установлены, помогите мне достичь их как можно быстрее. Вам нужно только предоставить содержание ответа.";
"promot_local"="Сейчас вы играете роль пользователя, в то время как пользователь играет роль человека, с которым он общается. Пожалуйста, убедитесь, что ваши ответы соответствуют сценарию, установленному пользователем, поддерживая связность и естественность. Если цели установлены, помогите пользователю достичь их как можно быстрее. Обратите внимание, что записи чатов распознаются из изображений, что может привести к несоответствиям.";
"Import_local"="Локальное распознавание";
"Import_remote"="Облачное распознавание";
"Relationship Background" = "Фоновая информация об отношениях";
//"chat_help"="点击标题编辑情境信息,点击新建配置新的情境";
"chat_help"="Нажмите на заголовок, чтобы изменить информацию о контексте, нажмите, чтобы создать новый контекст";
"edit" = "Редактировать";
"Me"="Я";
"对方"="Цель";
"使用"="Использовать";
"Guest"="Гость";
"收到的的验证码"="Полученный код подтверждения";
"允许广告" = "Разрешить рекламу";
"广告提示" = "Пожалуйста, разрешите показывать рекламу, чтобы поддержать наше развитие. Настройка будет сбрасываться при каждом перезапуске. Вы сможете отключить её навсегда после пополнения баланса.";
"Other Apps" = "Другие приложения";
"打开" = "Открыть";
"下载" = "Скачать";
"其他应用" = "Другие приложения";
"关于" = "О программе";
"chat4o" = "Chat4o";
"chat4o_description" = "Чат для всех, универсальный AI-ассистент! Помогает решать различные жизненные, рабочие, учебные и эмоциональные вопросы с помощью интеллектуального общения. Будь то повседневные задачи или сложные вызовы, он предоставляет эффективные и персонализированные решения.";
"textGame" = "Story Maker";
"textGame_description" = "Интерактивная текстовая игра, где вы можете выбирать ход сюжета и давать волю своему воображению!";
"chatAdvisor" = "Chat Advisor";
"chatAdvisor_description" = "Позвольте передовому ИИ создать для вас ответы в чате.";
"通过Tiktok登录" = "Войти через Tiktok";
"帮你聊天" = "Пусть самый продвинутый ИИ поможет вам в чате";
"继续使用" = "Продолжая использовать, вы соглашаетесь с нашими";
"Apple登录" = "Войти через Apple";

// Строки, связанные с обновлением версии
"version_update_force_title" = "Требуется обновление";
"version_update_optional_title" = "Доступно обновление";
"version_update_subtitle" = "Версия %@ теперь доступна";
"version_update_required" = "Обязательно";
"version_update_available" = "Доступно";
"version_current" = "Текущая";
"version_latest" = "Последняя";
"version_update_button_update" = "Обновить сейчас";
"version_update_button_later" = "Позже";
"version_update_button_skip" = "Пропустить эту версию";
"version_update_default_message" = "Доступна новая версия. Пожалуйста, обновитесь для лучшего опыта.";
"version_update_force_message" = "Эта версия содержит важные обновления безопасности. Вы должны обновиться, чтобы продолжить использование приложения.";
