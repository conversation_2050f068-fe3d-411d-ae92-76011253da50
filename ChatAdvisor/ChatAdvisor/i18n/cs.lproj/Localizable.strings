// Czech
"empty_session" = "Momentálně žádný rozhovor";
"个人" = "Osobní";
"以确认删除" = "Pro potvrzení smazání";
"价格" = "Cena";
"余额" = "Zůstatek";
"充值" = "Nabití";
"充值结果" = "Výsledek dobíjení";
"充值金额" = "Částka dobíjení";
"删除" = "Smazat";
"删除确认" = "Potvrzení smazání";
"删除账号" = "Smazat účet";
"发送" = "Odeslat";
"发送邮件" = "Odeslat email";
"取消归档" = "Zrušit archivaci";
"口吻" = "Tón";
"复制" = "Kopírovat";
"好的" = "OK";
"字" = "Znak";
"密码" = "Heslo";
"已复制" = "Zkopírováno";
"已归档" = "Archivováno";
"开启后会话将模仿口吻回复" = "Po povolení bude konverzace napodobovat tón odpovědi";
"开始新的对话吧" = "Začněte nový rozhovor";
"归档" = "Archiv";
"当前余额:" = "Aktuální zůstatek:";
"您确定要删除账号吗？" = "Jste si jistí, že chcete smazat svůj účet?";
"意见与建议" = "Názory a návrhy";
"我确定删除账号" = "Potvrzuji smazání účtu";
"搜索聊天" = "Hledat chat";
"暂无归档" = "Žádné archivy";
"服务条款" = "Podmínky služby";
"朗读" = "Přečíst nahlas";
"条款" = "Podmínky";
"每" = "Za";
"没有标题的会话" = "Rozhovor bez názvu";
"注册" = "Registrace";
"版本" = "Verze";
"用户" = "Uživatel";
"登出" = "Odhlásit se";
"登录" = "Přihlásit se";
"确定" = "Potvrdit";
"确认删除输入错误, 请重新输入" = "Potvrzení smazání je nesprávné, prosím zadejte znovu";
"秒后重发" = "sekundy k opětovnému odeslání";
"约" = "O";
"设置" = "Nastavení";
"请输入" = "Prosím zadejte";
"请输入你的邮箱地址和密码" = "Prosím zadejte svou emailovou adresu a heslo";
"请输入你的邮箱地址，以及大于6位数的密码" = "Prosím zadejte svou emailovou adresu a heslo delší než 6 znaků";
"请输入新的聊天标题" = "Prosím zadejte nový název chatu";
"账号删除后无法恢复" = "Smazání účtu smaže všechna data a nelze je obnovit";
"输入:" = "Vstup:";
"输出:" = "Výstup:";
"邮箱" = "Email";
"邮箱登录" = "Přihlášení emailem";
"重命名" = "Přejmenovat";
"隐私政策" = "Zásady ochrany osobních údajů";
"震动反馈" = "Zpětná vazba vibrací";
"验证" = "Ověření";
"昨天" = "Včera";
"今天" = "Dnes";
"年" = "Rok";
"月" = "Měsíc";
"日" = "Den";
"收费标准" = "Poplatkový standard";
"取消"="Zrušit";
"服务器内部错误"="Interní serverová chyba";
"当前设备没有开启应用内购买功能"="Aktuální zařízení nemá povolen nákup v aplikaci";
"获取商品失败"="Nepodařilo se získat zboží";
"充值说明"="Pokyny pro dobíjení";
"购买失败"="Nákup se nezdařil";
"重试"="Zkusit znovu";
"完整会话"="Úplná relace";
"开启可以让对话回答更精准,可能会加快余额消耗"="Povolení může umožnit přesnější odpovědi v rozhovoru, což může zrychlit spotřebu zůstatku";
"语音"="Hlas";
"语音识别"="Vyberte jazyk pro rozpoznávání hlasu pro zvýšení přesnosti";
"选择语言"="Vyberte jazyk";
"通过Google登录"="Přihlásit se pomocí Google";
"通过Facebook登录"="Přihlásit se pomocí Facebook";
"通过Twitter登录"="Přihlásit se pomocí Twitter";
"主色调"="Hlavní barva";
"Apple登录" = "Přihlásit se pomocí Apple";

"done"="Hotovo";
"Previous"="Předchozí";
"Next"="Další";
"History"="Historie";
"Context"="Kontext";
"Become friends"="Stát se přáteli";
"Choose Please"="Prosím vyberte";
// Relationships
"Friend"="Přítel";
"Family"="Rodina";
"Colleague"="Kolega";
"Stranger"="Cizinec";
"Neighbor"="Soused";
"Classmate"="Spolužák";
"Teammate"="Spoluhráč";
"Acquaintance"="Známý";
"Mentor"="Mentor";
"Mentee"="Mentee";
"Business Partner"="Obchodní partner";
"Romantic Partner"="Romantický partner";
"Spouse"="Manžel/ka";
"Ex-Partner"="Bývalý partner";
"Sibling"="Sourozenec";
"Parent"="Rodič";
"Child"="Dítě";
"Grandparent"="Prarodič";
"Grandchild"="Vnuk/Vnučka";
"In-law"="Příbuzný";
"Coworker"="Kolega";
"Boss"="Šéf";
"Subordinate"="Podřízený";
"Client"="Klient";
"Tutor"="Učitel";
"Roommate"="Spolubydlící";
"Lab Partner"="Laboratorní partner";
"Study Buddy"="Studijní partner";
"Project Teammate"="Projektový partner";
"Online Friend"="Online přítel";
"Pen Pal"="Dopisní přítel";
"Travel Companion"="Cestovní společník";
"Sports Teammate"="Sportovní spoluhráč";
"Doctor"="Doktor";
"Patient"="Pacient";
"Customer Service"="Zákaznický servis";
"Supplier"="Dodavatel";
"Landlord"="Pronajímatel";
"Tenant"="Nájemník";
"Business Competitor"="Obchodní konkurent";
// Degrees
"Close"="Blízký";
"Casual"="Neformální";
"Distant"="Vzdálený";
"Professional"="Profesionální";
"Complicated"="Složitý";
"Trusted"="Důvěryhodný";
"Intimate"="Intimní";
// Styles
"Formal"="Formální";
"Casual"="Neformální";
"Friendly"="Přátelský";
"Professional"="Profesionální";
"Humorous"="Humorný";
"Direct"="Přímý";
"Empathetic"="Empatický";
"Supportive"="Podpůrný";
"Inquisitive"="Zvídavý";
// Response Lengths
"Short"="Krátký";
"Medium"="Střední";
"Long"="Dlouhý";
"Detailed"="Detailní";
// Forms
"Information"="Informace";
"Preferences"="Předvolby";
"Emotion"="Emoce";
"Custom"="Vlastní";
"Chat_history"="Importovat historii chatu";
"Your Gender"="Vaše pohlaví";
"Your Age"="Váš věk";
"Chat Partner's Gender"="Pohlaví partnera chatu";
"Chat Partner's Age"="Věk partnera chatu";
"Known Since"="Znám od";
"Topics"="Témata";
"Goal"="Cíl";
"Chat Partner's Emotion"="Emoce partnera chatu";
"Preferred Emotion in Responses"="Preferovaná emoce v odpovědích";
"Preferred_Chat_Style"="Preferovaný styl chatu";
"Preferred_Response_Length"="Preferovaná délka odpovědi";
"Relationship_Type"="Typ vztahu";
"Relationship_Status"="Status vztahu";
"form_help"="
### Rychlé přepínání kroků
- Přejetím vlevo nebo vpravo nebo kliknutím na kroky výše můžete rychle přepínat kroky.
### Nepovinný obsah
- Veškerý obsah je nepovinný.
- Čím podrobnější je vstup, tím přesnější budou odpovědi AI.
### Importovat historii chatu
- Kliknutím na tlačítko níže zahájíte importování snímků obrazovky chatu.
";
"promot_cloud"="Poskytnu kontext nebo cíl a doufám, že na základě těchto informací a v mém tónu a perspektivě vytvoříte vhodnou odpověď. Pokud jsou stanoveny cíle, pomozte mi je dosáhnout co nejrychleji. Stačí poskytnout obsah odpovědi.";
"promot_local"="Nyní hrajete roli uživatele, zatímco uživatel bude hrát roli osoby, se kterou si povídá. Ujistěte se, že vaše odpovědi odpovídají scénáři stanovenému uživatelem, a udržujte koherenci a přirozenost. Pokud jsou cíle stanoveny, pomozte uživateli dosáhnout jich co nejrychleji. Mějte na paměti, že záznamy chatu jsou texty rozpoznané z obrázků, což může vést k nesrovnalostem.";
"Import_local"="Místnírozpoznávání";
"Import_remote"="Cloudovérozpoznávání";
"Relationship Background"="Pozadí vztahu";
//"chat_help"="点击标题编辑情境信息,点击新建配置新的情境";
"chat_help"="Kliknutím na nadpis upravíte informace o kontextu, kliknutím na tlačítko vytvoříte nový kontext";
"edit" = "Upravit";
"Me"="Já";
"对方"="Cíl";
"使用"="Použití";
"Guest"="Host";
"收到的的验证码"="Ověřovací kód";
"允许广告" = "Povolit reklamy";
"广告提示" = "Prosím, povolte reklamy, abyste podpořili náš lepší vývoj. Při každém restartu se resetuje. Můžete jej trvale vypnout po dobití。";
"Other Apps" = "Další aplikace";
"打开" = "Otevřít";
"下载" = "Stáhnout";
"其他应用" = "Další aplikace";
"关于" = "O aplikaci";
"chat4o" = "Chat4o";
"chat4o_description" = "Chat pro všechny, ultimátní AI asistent! Pomáhá řešit různé problémy v životě, práci, studiu a emocích prostřednictvím inteligentních rozhovorů. Ať už jde o každodenní úkoly nebo složité výzvy, nabízí efektivní, personalizovaná řešení。";
"帮你聊天" = "Nechte nejpokročilejší AI, aby vám pomohla chatovat";
"继续使用" = "Pokračováním v používání souhlasíte s našimi";
"textGame" = "Story Maker";
"textGame_description" = "Interaktivní textová hra, ve které si můžete svobodně vybrat směr příběhu a nechat svou představivost běžet divoce!";
"chatAdvisor" = "Chat Advisor";
"chatAdvisor_description" = "Nechte pokročilou AI generovat odpovědi na chat za vás。";
"通过Tiktok登录" = "Přihlásit se přes Tiktok";
