/* 
  Localizable.strings
  JunShi

  Created by <PERSON><PERSON><PERSON><PERSON> on 2024/6/2.
  
*/

// Indonesian
"empty_session" = "No conversation at the moment";
"个人" = "Personal";
"以确认删除" = "To confirm deletion";
"价格" = "Price";
"余额" = "Balance";
"充值" = "Top up";
"充值结果" = "Top-up result";
"充值金额" = "Top-up amount";
"删除" = "Delete";
"删除确认" = "Delete confirmation";
"删除账号" = "Delete account";
"发送" = "Send";
"发送邮件" = "Send email";
"取消归档" = "Cancel archive";
"口吻" = "Tone";
"复制" = "Copy";
"好的" = "OK";
"字" = "Character";
"密码" = "Password";
"已复制" = "Copied";
"已归档" = "Archived";
"开启后会话将模仿口吻回复" = "After enabling, the conversation will mimic the tone of the reply";
"开始新的对话吧" = "Start a new conversation";
"归档" = "Archive";
"当前余额:" = "Current balance:";
"您确定要删除账号吗？" = "Are you sure you want to delete your account?";
"意见与建议" = "Feedback and suggestions";
"我确定删除账号" = "I confirm deletion of account";
"搜索聊天" = "Search chat";
"暂无归档" = "No archives";
"服务条款" = "Terms of service";
"朗读" = "Read aloud";
"条款" = "Terms";
"每" = "Per";
"没有标题的会话" = "No title";
"注册" = "Sign up";
"版本" = "Version";
"用户" = "User";
"登出" = "Log out";
"登录" = "Log in";
"确定" = "Confirm";
"确认删除输入错误, 请重新输入" = "Confirmation of deletion input error, please re-enter";
"秒后重发" = "seconds to resend";
"约" = "About";
"设置" = "Settings";
"请输入" = "Please enter";
"请输入你的邮箱地址和密码" = "Please enter your email address and password";
"请输入你的邮箱地址，以及大于6位数的密码" = "Please enter your email address and a password greater than 6 characters";
"请输入新的聊天标题" = "Please enter a new chat title";
"账号删除后无法恢复" = "Account deletion will clear all data and cannot be recovered";
"输入:" = "Input:";
"输出:" = "Output:";
"邮箱" = "Email";
"邮箱登录" = "Email login";
"重命名" = "Rename";
"隐私政策" = "Privacy Policy";
"震动反馈" = "Vibration feedback";
"验证" = "Verification";
"昨天" = "Yesterday";
"今天" = "Today";
"年" = "Year";
"月" = "Month";
"日" = "Day";
"收费标准" = "Charging standard";
"取消"="Cancel";
"服务器内部错误"="Internal server error";
"当前设备没有开启应用内购买功能"="The current device does not have in-app purchase enabled";
"获取商品失败"="Failed to get goods";
"充值说明"="Tips";
"购买失败"="Purchase failed";
"重试"="Retry";
"完整会话"="Full Session";
"开启可以让对话回答更精准,可能会加快余额消耗"="Enabling can make the conversation answer more accurately, which may speed up the balance consumption";
"语音"="Voice";
"语音识别"="Select the language for voice recognition to enhance accuracy";
"选择语言"="Select Language";
"通过Google登录"="Login with Google";
"通过Facebook登录"="Login with Facebook";
"通过Twitter登录"="Login with Twitter";
"主色调"="Main color";
"帮你聊天" = "Biarkan AI paling canggih membantu Anda mengobrol";
"继续使用" = "Dengan melanjutkan penggunaan, Anda setuju dengan kami";

// chatadvisor
"done" = "Selesai";
"Previous" = "Sebelumnya";
"Next" = "Selanjutnya";
"History" = "Riwayat";
"Context" = "Konteks";
"Become friends" = "Menjadi teman";
"Choose Please" = "Silakan pilih";
// Relationships
"Friend" = "Teman";
"Family" = "Keluarga";
"Colleague" = "Rekan";
"Stranger" = "Orang asing";
"Neighbor" = "Tetangga";
"Classmate" = "Teman sekelas";
"Teammate" = "Rekan tim";
"Acquaintance" = "Kenalan";
"Mentor" = "Mentor";
"Mentee" = "Binaan";
"Business Partner" = "Mitra bisnis";
"Romantic Partner" = "Pasangan romantis";
"Spouse" = "Pasangan hidup";
"Ex-Partner" = "Mantan pasangan";
"Sibling" = "Saudara";
"Parent" = "Orang tua";
"Child" = "Anak";
"Grandparent" = "Kakek/Nenek";
"Grandchild" = "Cucu";
"In-law" = "Menantu/mertua";
"Coworker" = "Rekan kerja";
"Boss" = "Atasan";
"Subordinate" = "Bawahan";
"Client" = "Klien";
"Tutor" = "Pengajar";
"Roommate" = "Temnan sekamar";
"Lab Partner" = "Rekan lab";
"Study Buddy" = "Teman belajar";
"Project Teammate" = "Rekan tim proyek";
"Online Friend" = "Teman online";
"Pen Pal" = "Sahabat pena";
"Travel Companion" = "Teman perjalanan";
"Sports Teammate" = "Rekan tim olahraga";
"Doctor" = "Dokter";
"Patient" = "Pasien";
"Customer Service" = "Layanan pelanggan";
"Supplier" = "Pemasok";
"Landlord" = "Pemilik rumah";
"Tenant" = "Penyewa";
"Business Competitor" = "Pes konkuren bisnis";
// Degrees
"Close" = "Dekat";
"Casual" = "Kasual";
"Distant" = "Jauh";
"Professional" = "Profesional";
"Complicated" = "Rumit";
"Trusted" = "Dipercaya";
"Intimate" = "Intim";
// Styles
"Formal" = "Formal";
"Casual" = "Kasual";
"Friendly" = "Ramah";
"Professional" = "Profesional";
"Humorous" = "Lucu";
"Direct" = "Langsung";
"Empathetic" = "Empatis";
"Supportive" = "Penuh dukungan";
"Inquisitive" = "Penuh rasa ingin tahu";
// Response Lengths
"Short" = "Singkat";
"Medium" = "Sedang";
"Long" = "Panjang";
"Detailed" = "Detail";
// Forms
"Information" = "Informasi";
"Preferences" = "Preferensi";
"Emotion" = "Emosi";
"Custom" = "Kustom";
"Chat_history" = "Impor Riwayat Obrolan";
"Your Gender" = "Jenis kelamin Anda";
"Your Age" = "Usia Anda";
"Chat Partner's Gender" = "Jenis kelamin mitra obrolan";
"Chat Partner's Age" = "Usia mitra obrolan";
"Known Since" = "Dikenal sejak";
"Topics" = "Topik";
"Goal" = "Tujuan";
"Chat Partner's Emotion" = "Emosi mitra obrolan";
"Preferred Emotion in Responses" = "Emosi yang diinginkan dalam tanggapan";
"Preferred_Chat_Style" = "Gaya obrolan yang diinginkan";
"Preferred_Response_Length" = "Panjang tanggapan yang diinginkan";
"Relationship_Type" = "Jenis hubungan";
"Relationship_Status" = "Status hubungan";
"form_help" = "
### Pergantian Langkah Cepat
- Geser ke kiri atau kanan pada langkah-langkah di atas atau klik untuk beralih langkah dengan cepat.
### Konten Opsional
- Semua konten bersifat opsional.
- Semakin detail masukan yang diberikan, semakin akurat tanggapan AI.
### Impor Riwayat Obrolan
- Klik tombol di bawah untuk memulai mengimpor tangkapan layar obrolan.";
"promot_cloud"="Saya akan memberikan konteks atau tujuan, dan saya berharap Anda dapat menghasilkan respons yang sesuai berdasarkan informasi ini dan dalam nada serta perspektif saya. Jika tujuan ditetapkan, bantu saya mencapainya secepat mungkin. Anda hanya perlu memberikan isi dari tanggapan.";
"promot_local" = "Anda sekarang memainkan peran sebagai pengguna, sementara pengguna akan memainkan peran sebagai orang yang mereka ajak bicara. Pastikan respons Anda sesuai dengan skenario yang ditetapkan oleh pengguna, dan pertahankan konsistensi dan kelancaran. Jika tujuan telah ditetapkan, bantu pengguna mencapainya secepat mungkin. Harap diperhatikan bahwa catatan obrolan adalah teks yang dikenali dari gambar, yang dapat menyebabkan ketidaksesuaian.";
"Import_local" = "Pengakuan lokal";
"Import_remote" = "Pengakuan cloud";
"Relationship Background" = "Latar Belakang Hubungan";
//"chat_help"="点击标题编辑情境信息,点击新建配置新的情境";
"chat_help"="Klik judul untuk mengedit informasi konteks, klik untuk membuat konfigurasi baru";
"edit" = "Edit";
"Me"="Saya";
"对方"="Tujuan";
"使用"="Gunakan";
"Guest"="Pengguna";
"收到的的验证码"="Kode verifikasi yang diterima";
"允许广告" = "Izinkan iklan";
"广告提示" = "Silakan izinkan iklan untuk mendukung pengembangan kami yang lebih baik. Ini akan direset setiap kali Anda memulai ulang. Anda dapat mematikannya secara permanen setelah mengisi ulang.";
"Other Apps" = "Aplikasi Lain";
"打开" = "Buka";
"下载" = "Unduh";
"其他应用" = "Aplikasi Lain";
"关于" = "Tentang";
"chat4o" = "Chat4o";
"chat4o_description" = "Sebuah chat untuk semua orang, asisten AI terbaik! Membantu menyelesaikan berbagai masalah kehidupan, pekerjaan, studi, dan emosi melalui percakapan cerdas. Baik itu tugas sehari-hari atau tantangan kompleks, ia menyediakan solusi yang efisien dan dipersonalisasi.";
"textGame" = "Story Maker";
"textGame_description" = "Permainan cerita berbasis teks interaktif, di mana Anda bebas memilih jalannya cerita dan membiarkan imajinasi Anda berkembang!";
"chatAdvisor" = "Chat Advisor";
"chatAdvisor_description" = "Biarkan AI canggih menghasilkan respons chat untuk Anda.";
"通过Tiktok登录" = "Masuk dengan Tiktok";
"Apple登录" = "Masuk dengan Apple";
