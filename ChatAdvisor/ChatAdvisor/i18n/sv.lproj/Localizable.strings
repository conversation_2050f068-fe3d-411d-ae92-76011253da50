// Swedish
"empty_session" = "Ingen konversation för tillfället";
"个人" = "Personlig";
"以确认删除" = "För att bekräfta radering";
"价格" = "Pris";
"余额" = "Saldo";
"充值" = "Ladda upp";
"充值结果" = "Uppladdningsresultat";
"充值金额" = "Uppladdningsbelopp";
"删除" = "Radera";
"删除确认" = "Bekräfta radering";
"删除账号" = "Radera konto";
"发送" = "Skicka";
"发送邮件" = "Skicka e-post";
"取消归档" = "Avbryt arkivering";
"口吻" = "Ton";
"复制" = "Kopiera";
"好的" = "OK";
"字" = "Tecken";
"密码" = "Lösenord";
"已复制" = "Kopierat";
"已归档" = "Arkiverat";
"开启后会话将模仿口吻回复" = "Efter aktivering kommer konversationen att imitera tonen i svaret";
"开始新的对话吧" = "Starta en ny konversation";
"归档" = "Arkivera";
"当前余额:" = "Aktuellt saldo:";
"您确定要删除账号吗？" = "Är du säker på att du vill radera ditt konto?";
"意见与建议" = "Feedback och förslag";
"我确定删除账号" = "Jag bekräftar radering av konto";
"搜索聊天" = "Sök konversation";
"暂无归档" = "Inga arkiv";
"服务条款" = "Tjänstevillkor";
"朗读" = "Läs högt";
"条款" = "Villkor";
"每" = "Per";
"没有标题的会话" = "Konversation utan titel";
"注册" = "Registrera";
"版本" = "Version";
"用户" = "Användare";
"登出" = "Logga ut";
"登录" = "Logga in";
"确定" = "Bekräfta";
"确认删除输入错误, 请重新输入" = "Fel vid bekräftelse av radering, vänligen ange igen";
"秒后重发" = "sekunder till återutsändning";
"约" = "Om";
"设置" = "Inställningar";
"请输入" = "Vänligen ange";
"请输入你的邮箱地址和密码" = "Vänligen ange din e-postadress och lösenord";
"请输入你的邮箱地址，以及大于6位数的密码" = "Vänligen ange din e-postadress och ett lösenord längre än 6 tecken";
"请输入新的聊天标题" = "Vänligen ange en ny chattitel";
"账号删除后无法恢复" = "Kontoradering kommer att rensa all data och kan inte återställas";
"输入:" = "Inmatning:";
"输出:" = "Utmatning:";
"邮箱" = "E-post";
"邮箱登录" = "E-postinloggning";
"重命名" = "Byt namn";
"隐私政策" = "Integritetspolicy";
"震动反馈" = "Vibrationsfeedback";
"验证" = "Verifiering";
"昨天" = "Igår";
"今天" = "Idag";
"年" = "År";
"月" = "Månad";
"日" = "Dag";
"收费标准" = "Prissättningsstandard";
"取消"="Avbryt";
"服务器内部错误"="Internt serverfel";
"当前设备没有开启应用内购买功能"="Den aktuella enheten har inte aktiverat köp i appen";
"获取商品失败"="Misslyckades att få varor";
"充值说明"="Tips";
"购买失败"="Köp misslyckades";
"重试"="Försök igen";
"完整会话"="Fullständig session";
"开启可以让对话回答更精准,可能会加快余额消耗"="Aktivering kan göra konversationssvar mer exakta, vilket kan påskynda saldoanvändningen";
"语音"="Röst";
"语音识别"="Välj språket för röstigenkänning för att förbättra noggrannheten";
"选择语言"="Välj språk";
"通过Google登录"="Logga in med Google";
"通过Facebook登录"="Logga in med Facebook";
"通过Twitter登录"="Logga in med Twitter";
"通过Tiktok登录" = "Logga in med Tiktok";
"Apple登录" = "Logga in med Apple";
"主色调"="Huvudfärg";
"帮你聊天" = "Låt den mest avancerade AI hjälpa dig att chatta";
"继续使用" = "Genom att fortsätta använda, godkänner du våra";

// chatadvisor
"done" = "Klart";
"Previous" = "Föregående";
"Next" = "Nästa";
"History" = "Historik";
"Context" = "Sammanhang";
"Become friends" = "Bli vänner";
"Choose Please" = "Välj, tack";
// Relationships
"Friend" = "Vän";
"Family" = "Familj";
"Colleague" = "Kollega";
"Stranger" = "Främling";
"Neighbor" = "Granne";
"Classmate" = "Klasskamrat";
"Teammate" = "Lagkamrat";
"Acquaintance" = "Bekant";
"Mentor" = "Mentor";
"Mentee" = "Mentee";
"Business Partner" = "Affärspartner";
"Romantic Partner" = "Romantisk partner";
"Spouse" = "Make/maka";
"Ex-Partner" = "Före detta partner";
"Sibling" = "Syskon";
"Parent" = "Förälder";
"Child" = "Barn";
"Grandparent" = "Mor/farförälder";
"Grandchild" = "Barnbarn";
"In-law" = "Svärförälder";
"Coworker" = "Kollega";
"Boss" = "Chef";
"Subordinate" = "Underordnad";
"Client" = "Kund";
"Tutor" = "Lärare";
"Roommate" = "Rums-kamrat";
"Lab Partner" = "Labbpartner";
"Study Buddy" = "Studiekompis";
"Project Teammate" = "Projektkamrat";
"Online Friend" = "Online-vän";
"Pen Pal" = "Brevvän";
"Travel Companion" = "Reskamrat";
"Sports Teammate" = "Idrottskamrat";
"Doctor" = "Doktor";
"Patient" = "Patient";
"Customer Service" = "Kundtjänst";
"Supplier" = "Leverantör";
"Landlord" = "Hyresvärd";
"Tenant" = "Hyresgäst";
"Business Competitor" = "Konkurrent i affärer";
// Degrees
"Close" = "Nära";
"Casual" = "Avslappnad";
"Distant" = "Avlägsen";
"Professional" = "Professionell";
"Complicated" = "Komplex";
"Trusted" = "Pålitlig";
"Intimate" = "Intim";
// Styles
"Formal" = "Formell";
"Casual" = "Avslappnad";
"Friendly" = "Vänlig";
"Professional" = "Professionell";
"Humorous" = "Humoristisk";
"Direct" = "Direkt";
"Empathetic" = "Empatisk";
"Supportive" = "Stödjande";
"Inquisitive" = "Frågvis";
// Response Lengths
"Short" = "Kort";
"Medium" = "Medium";
"Long" = "Lång";
"Detailed" = "Detaljerad";
// Forms
"Information" = "Information";
"Preferences" = "Preferenser";
"Emotion" = "Känsla";
"Custom" = "Anpassad";
"Chat_history" = "Importera Chatt Historik";
"Your Gender" = "Ditt kön";
"Your Age" = "Din ålder";
"Chat Partner's Gender" = "Chatt-partners kön";
"Chat Partner's Age" = "Chatt-partners ålder";
"Known Since" = "Känd sedan";
"Topics" = "Ämnen";
"Goal" = "Mål";
"Chat Partner's Emotion" = "Chatt-partners känsla";
"Preferred Emotion in Responses" = "Föredragen känsla i svar";
"Preferred_Chat_Style" = "Föredragen chatt-stil";
"Preferred_Response_Length" = "Föredragen svarslängd";
"Relationship_Type" = "Relationstyp";
"Relationship_Status" = "Relationsstatus";
"form_help" = "
### Snabb Steg Växling
- Dra åt vänster eller höger på stegen ovan eller klicka på dem för att snabbt byta steg.
### Valfritt Innehåll
- Allt innehåll är frivilligt.
- Ju mer detaljerad inmatning, desto mer exakta AI-svar.
### Importera Chatt Historik
- Klicka på knappen nedan för att börja importera chattskärmbilder.";
"promot_cloud"="Jag kommer att ge en kontext eller ett mål, och jag hoppas att du kan generera ett lämpligt svar baserat på denna information och i min ton och perspektiv. Om mål är satta, hjälp mig att uppnå dem så snabbt som möjligt. Du behöver bara tillhandahålla innehållet i svaret.";
"promot_local" = "Du spelar nu rollen som användaren, medan användaren kommer att spela rollen som personen de chattar med. Se till att dina svar är i linje med det scenario som användaren har satt och bibehåll sammanhanget och naturligheten. Om mål har satts, hjälp användaren att uppnå dem så snabbt som möjligt. Observera att chatthistoriken är text som är igenkänd från bilder, vilket kan leda till avvikelser.";
"Import_local" = "Lokal igenkänning";
"Import_remote" = "Moln-igenkänning";
"Relationship Background" = "Relationsbakgrund";
//"chat_help"="点击标题编辑情境信息,点击新建配置新的情境";
"chat_help"="Klicka på titeln för att redigera kontextinformation, klicka på Skapa ny för att konfigurera en ny kontext";
"edit" = "Redigera";
"Me"="Jag";
"对方"="Mål";
"使用"="Använd";
"Guest"="Gäst";
"收到的的验证码"="Mottaget verifieringskod";
"允许广告" = "Tillåt annonser";
"广告提示" = "Vänligen tillåt annonser för att stödja vår bättre utveckling. Detta kommer att återställas varje gång du startar om. Du kan permanent stänga av det efter att ha laddat.";
"Other Apps" = "Andra appar";
"打开" = "Öppna";
"下载" = "Ladda ner";
"其他应用" = "Andra appar";
"关于" = "Om";
"chat4o" = "Chat4o";
"chat4o_description" = "En chat för alla, den ultimata AI-assistenten! Hjälper till att lösa olika livs-, arbets-, studie- och känslomässiga problem genom intelligenta samtal. Oavsett om det handlar om dagliga uppgifter eller komplexa utmaningar, ger den effektiva och personliga lösningar.";
"textGame" = "Story Maker";
"textGame_description" = "Ett interaktivt textbaserat berättelsespel där du fritt kan välja berättelsens riktning och låta din fantasi flöda!";
"chatAdvisor" = "Chat Advisor";
"chatAdvisor_description" = "Låt avancerad AI generera chatt-svar för dig.";
"通过Tiktok登录" = "Logga in med Tiktok";
"Apple登录" = "Logga in med Apple";
