//
//  SecondRoundDialogueTests.swift
//  ChatAdvisor
//
//  Created by AI Assistant on 2025-01-01.
//

import XCTest
import Combine
@testable import ChatAdvisor

class SecondRoundDialogueTests: XCTestCase {
    var chatViewModel: ChatViewModel!
    var chatListViewModel: ChatListViewModel!
    var cancellables: Set<AnyCancellable>!
    
    override func setUp() {
        super.setUp()
        chatListViewModel = ChatListViewModel()
        chatViewModel = ChatViewModel(chatListViewModel: chatListViewModel)
        cancellables = Set<AnyCancellable>()
    }
    
    override func tearDown() {
        chatViewModel = nil
        chatListViewModel = nil
        cancellables = nil
        super.tearDown()
    }
    
    // MARK: - 状态管理测试
    
    func testInitialState() {
        // 验证初始状态
        XCTAssertFalse(chatViewModel.isAnswering, "初始状态isAnswering应该为false")
        XCTAssertFalse(chatViewModel.showTypingIndicator, "初始状态showTypingIndicator应该为false")
        XCTAssertEqual(chatViewModel.aiTypingState, .idle, "初始状态aiTypingState应该为idle")
        XCTAssertNil(chatViewModel.currentStreamingMessageId, "初始状态currentStreamingMessageId应该为nil")
        XCTAssertNil(chatViewModel.currentLocalResponseId, "初始状态currentLocalResponseId应该为nil")
    }
    
    func testStateResetAfterError() {
        // 测试错误后状态重置
        
        // 模拟设置为处理中状态
        chatViewModel.isAnswering = true
        chatViewModel.showTypingIndicator = true
        chatViewModel.aiTypingState = .typing
        chatViewModel.currentStreamingMessageId = "test-id"
        chatViewModel.currentLocalResponseId = "test-local-id"
        
        // 调用failedToast，应该重置所有状态
        chatViewModel.failedToast("测试错误")
        
        // 验证状态被正确重置
        XCTAssertFalse(chatViewModel.isAnswering, "错误后isAnswering应该被重置为false")
        XCTAssertFalse(chatViewModel.showTypingIndicator, "错误后showTypingIndicator应该被重置为false")
        XCTAssertEqual(chatViewModel.aiTypingState, .idle, "错误后aiTypingState应该被重置为idle")
        XCTAssertNil(chatViewModel.currentStreamingMessageId, "错误后currentStreamingMessageId应该被重置为nil")
        XCTAssertNil(chatViewModel.currentLocalResponseId, "错误后currentLocalResponseId应该被重置为nil")
        
        // 验证错误提示状态
        XCTAssertTrue(chatViewModel.isRequestError, "应该设置错误状态")
        XCTAssertTrue(chatViewModel.showToast, "应该显示错误提示")
        XCTAssertEqual(chatViewModel.toastMessage, "测试错误", "错误消息应该正确设置")
    }
    
    func testPreventDuplicateSendMessage() {
        // 测试防止重复发送消息
        
        // 设置为处理中状态
        chatViewModel.isAnswering = true
        
        let initialMessageCount = chatViewModel.currentChat.messages.count
        
        // 尝试发送消息，应该被阻止
        chatViewModel.sendMessage("测试消息")
        
        // 验证消息没有被添加
        XCTAssertEqual(chatViewModel.currentChat.messages.count, initialMessageCount, "处理中时不应该添加新消息")
        XCTAssertTrue(chatViewModel.isAnswering, "isAnswering状态不应该改变")
    }
    
    func testPreventDuplicatePostMessages() {
        // 测试防止重复调用postMessages
        
        // 设置为处理中状态
        chatViewModel.isAnswering = true
        
        // 记录初始状态
        let initialTypingState = chatViewModel.showTypingIndicator
        let initialAIState = chatViewModel.aiTypingState
        
        // 尝试调用postMessages，应该被阻止
        chatViewModel.postMessages(forChat: chatViewModel.currentChat)
        
        // 验证状态没有改变
        XCTAssertEqual(chatViewModel.showTypingIndicator, initialTypingState, "showTypingIndicator不应该改变")
        XCTAssertEqual(chatViewModel.aiTypingState, initialAIState, "aiTypingState不应该改变")
    }
    
    // MARK: - 超时机制测试
    
    func testTimeoutMechanism() {
        // 测试超时机制
        
        let expectation = XCTestExpectation(description: "超时应该触发错误处理")
        
        // 监听错误状态变化
        chatViewModel.$showToast
            .dropFirst()
            .sink { showToast in
                if showToast {
                    expectation.fulfill()
                }
            }
            .store(in: &cancellables)
        
        // 模拟开始请求（这会启动超时计时器）
        // 注意：在实际测试中，我们需要模拟网络请求
        // 这里我们直接测试超时逻辑
        
        wait(for: [expectation], timeout: 65.0) // 等待超过超时时间
    }
    
    // MARK: - 连续对话测试
    
    func testConsecutiveMessages() {
        // 测试连续发送消息的能力
        
        // 确保初始状态正确
        XCTAssertFalse(chatViewModel.isAnswering, "初始状态应该允许发送消息")
        
        // 发送第一条消息
        chatViewModel.sendMessage("第一条消息")
        
        let firstMessageCount = chatViewModel.currentChat.messages.count
        XCTAssertGreaterThan(firstMessageCount, 0, "第一条消息应该被添加")
        
        // 模拟第一条消息处理完成
        chatViewModel.isAnswering = false
        chatViewModel.showTypingIndicator = false
        chatViewModel.aiTypingState = .idle
        chatViewModel.currentStreamingMessageId = nil
        chatViewModel.currentLocalResponseId = nil
        
        // 发送第二条消息
        chatViewModel.sendMessage("第二条消息")
        
        let secondMessageCount = chatViewModel.currentChat.messages.count
        XCTAssertGreaterThan(secondMessageCount, firstMessageCount, "第二条消息应该被添加")
    }
    
    func testStateConsistencyAfterMultipleOperations() {
        // 测试多次操作后状态一致性
        
        // 模拟多次发送和错误
        for i in 1...5 {
            // 重置状态
            chatViewModel.isAnswering = false
            
            // 发送消息
            chatViewModel.sendMessage("消息 \(i)")
            
            // 模拟错误
            chatViewModel.failedToast("错误 \(i)")
            
            // 验证状态被正确重置
            XCTAssertFalse(chatViewModel.isAnswering, "第\(i)次操作后isAnswering应该为false")
            XCTAssertFalse(chatViewModel.showTypingIndicator, "第\(i)次操作后showTypingIndicator应该为false")
            XCTAssertEqual(chatViewModel.aiTypingState, .idle, "第\(i)次操作后aiTypingState应该为idle")
        }
    }
    
    // MARK: - 资源清理测试
    
    func testResourceCleanupOnDeinit() {
        // 测试deinit时的资源清理
        
        // 创建一个临时的ChatViewModel
        var tempViewModel: ChatViewModel? = ChatViewModel(chatListViewModel: chatListViewModel)
        
        // 设置一些状态
        tempViewModel?.isAnswering = true
        tempViewModel?.showTypingIndicator = true
        
        // 释放引用，触发deinit
        tempViewModel = nil
        
        // 验证资源被清理（这个测试主要是确保deinit不会崩溃）
        XCTAssertNil(tempViewModel, "ChatViewModel应该被正确释放")
    }
    
    // MARK: - 边界情况测试
    
    func testEmptyMessageHandling() {
        // 测试空消息处理
        
        let initialMessageCount = chatViewModel.currentChat.messages.count
        
        // 尝试发送空消息
        chatViewModel.sendMessage("")
        
        // 验证空消息也会被处理（根据业务逻辑）
        let newMessageCount = chatViewModel.currentChat.messages.count
        XCTAssertGreaterThan(newMessageCount, initialMessageCount, "空消息也应该被添加")
    }
    
    func testRapidConsecutiveCalls() {
        // 测试快速连续调用
        
        let initialMessageCount = chatViewModel.currentChat.messages.count
        
        // 快速连续发送多条消息
        for i in 1...10 {
            chatViewModel.sendMessage("快速消息 \(i)")
        }
        
        // 由于防重复机制，只有第一条消息应该被处理
        let finalMessageCount = chatViewModel.currentChat.messages.count
        XCTAssertEqual(finalMessageCount, initialMessageCount + 1, "快速连续调用时只应该处理第一条消息")
    }
    
    // MARK: - 集成测试
    
    func testCompleteDialogueFlow() {
        // 测试完整的对话流程
        
        // 1. 发送第一条消息
        chatViewModel.sendMessage("你好")
        XCTAssertTrue(chatViewModel.isAnswering, "发送消息后应该处于处理状态")
        
        // 2. 模拟收到响应并完成
        chatViewModel.isAnswering = false
        chatViewModel.showTypingIndicator = false
        chatViewModel.aiTypingState = .idle
        
        // 3. 发送第二条消息
        chatViewModel.sendMessage("再次问好")
        XCTAssertTrue(chatViewModel.isAnswering, "第二次发送消息后应该处于处理状态")
        
        // 4. 模拟错误
        chatViewModel.failedToast("网络错误")
        XCTAssertFalse(chatViewModel.isAnswering, "错误后应该重置处理状态")
        
        // 5. 发送第三条消息
        chatViewModel.sendMessage("重试消息")
        XCTAssertTrue(chatViewModel.isAnswering, "错误重置后应该能继续发送消息")
    }
}
