// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 56;
	objects = {

/* Begin PBXBuildFile section */
		5886860A2E162D0800A4C3FF /* ScrollStateManager.swift in Sources */ = {isa = PBXBuildFile; fileRef = 588685DC2E162D0800A4C3FF /* ScrollStateManager.swift */; };
		5886860B2E162D0800A4C3FF /* PredefinedOptionKey.swift in Sources */ = {isa = PBXBuildFile; fileRef = 588686012E162D0800A4C3FF /* PredefinedOptionKey.swift */; };
		5886860C2E162D0800A4C3FF /* ScrollPositionReader.swift in Sources */ = {isa = PBXBuildFile; fileRef = 588685DB2E162D0800A4C3FF /* ScrollPositionReader.swift */; };
		5886860D2E162D0800A4C3FF /* PinCodeInputView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 588685EB2E162D0800A4C3FF /* PinCodeInputView.swift */; };
		5886860E2E162D0800A4C3FF /* ChatsListView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 588685D62E162D0800A4C3FF /* ChatsListView.swift */; };
		5886860F2E162D0800A4C3FF /* WebView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 588685F92E162D0800A4C3FF /* WebView.swift */; };
		588686102E162D0800A4C3FF /* RegisterView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 588685EC2E162D0800A4C3FF /* RegisterView.swift */; };
		588686112E162D0800A4C3FF /* ArchivedChatView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 588685CB2E162D0800A4C3FF /* ArchivedChatView.swift */; };
		588686122E162D0800A4C3FF /* AboutView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 588685F22E162D0800A4C3FF /* AboutView.swift */; };
		588686132E162D0800A4C3FF /* RecognizeFullScreenView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 588685D12E162D0800A4C3FF /* RecognizeFullScreenView.swift */; };
		588686142E162D0800A4C3FF /* RecognizeSmallView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 588685D32E162D0800A4C3FF /* RecognizeSmallView.swift */; };
		588686152E162D0800A4C3FF /* RechargeView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 588685F72E162D0800A4C3FF /* RechargeView.swift */; };
		588686162E162D0800A4C3FF /* SplashCodeSyntaxHighlighter.swift in Sources */ = {isa = PBXBuildFile; fileRef = 588685CE2E162D0800A4C3FF /* SplashCodeSyntaxHighlighter.swift */; };
		588686172E162D0800A4C3FF /* TypingIndicator.swift in Sources */ = {isa = PBXBuildFile; fileRef = 588685DF2E162D0800A4C3FF /* TypingIndicator.swift */; };
		588686182E162D0800A4C3FF /* StepView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 588686042E162D0800A4C3FF /* StepView.swift */; };
		588686192E162D0800A4C3FF /* MultiStepFormViewModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 588686002E162D0800A4C3FF /* MultiStepFormViewModel.swift */; };
		5886861A2E162D0800A4C3FF /* ArchivedChatViewModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 588685CC2E162D0800A4C3FF /* ArchivedChatViewModel.swift */; };
		5886861B2E162D0800A4C3FF /* AppListView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 588685F32E162D0800A4C3FF /* AppListView.swift */; };
		5886861C2E162D0800A4C3FF /* SplashView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 588685FB2E162D0800A4C3FF /* SplashView.swift */; };
		5886861D2E162D0800A4C3FF /* EmptySessionView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 588685E12E162D0800A4C3FF /* EmptySessionView.swift */; };
		5886861E2E162D0800A4C3FF /* TextOutputFormat.swift in Sources */ = {isa = PBXBuildFile; fileRef = 588685CF2E162D0800A4C3FF /* TextOutputFormat.swift */; };
		5886861F2E162D0800A4C3FF /* UpdatePromptView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 588686082E162D0800A4C3FF /* UpdatePromptView.swift */; };
		588686202E162D0800A4C3FF /* TextSelectionView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 588685DE2E162D0800A4C3FF /* TextSelectionView.swift */; };
		588686212E162D0800A4C3FF /* RecognizeView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 588685D42E162D0800A4C3FF /* RecognizeView.swift */; };
		588686222E162D0800A4C3FF /* InputViewModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 588685E62E162D0800A4C3FF /* InputViewModel.swift */; };
		588686232E162D0800A4C3FF /* LoginView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 588685EA2E162D0800A4C3FF /* LoginView.swift */; };
		588686242E162D0800A4C3FF /* EmailLoginView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 588685E82E162D0800A4C3FF /* EmailLoginView.swift */; };
		588686252E162D0800A4C3FF /* OptimizedMessageView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 588685DA2E162D0800A4C3FF /* OptimizedMessageView.swift */; };
		588686262E162D0800A4C3FF /* MultiStepFormView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 588685FF2E162D0800A4C3FF /* MultiStepFormView.swift */; };
		588686272E162D0800A4C3FF /* InputingAnimationView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 588685E92E162D0800A4C3FF /* InputingAnimationView.swift */; };
		588686282E162D0800A4C3FF /* EnhancedLoadingView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 588685E22E162D0800A4C3FF /* EnhancedLoadingView.swift */; };
		588686292E162D0800A4C3FF /* ChatConfigDatabaseManager.swift in Sources */ = {isa = PBXBuildFile; fileRef = 588685FD2E162D0800A4C3FF /* ChatConfigDatabaseManager.swift */; };
		5886862A2E162D0800A4C3FF /* SideMenu.swift in Sources */ = {isa = PBXBuildFile; fileRef = 588685F02E162D0800A4C3FF /* SideMenu.swift */; };
		5886862B2E162D0800A4C3FF /* StepViewModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 588686052E162D0800A4C3FF /* StepViewModel.swift */; };
		5886862C2E162D0800A4C3FF /* ContentView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 588685EF2E162D0800A4C3FF /* ContentView.swift */; };
		5886862D2E162D0800A4C3FF /* PreferLanguageView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 588685F42E162D0800A4C3FF /* PreferLanguageView.swift */; };
		5886862E2E162D0800A4C3FF /* RecognizeMessageView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 588685D22E162D0800A4C3FF /* RecognizeMessageView.swift */; };
		5886862F2E162D0800A4C3FF /* InputBottomView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 588685E42E162D0800A4C3FF /* InputBottomView.swift */; };
		588686302E162D0800A4C3FF /* VerificationView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 588685ED2E162D0800A4C3FF /* VerificationView.swift */; };
		588686312E162D0800A4C3FF /* MessageBubblePreview.swift in Sources */ = {isa = PBXBuildFile; fileRef = 588686072E162D0800A4C3FF /* MessageBubblePreview.swift */; };
		588686322E162D0800A4C3FF /* PurchaseView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 588685F62E162D0800A4C3FF /* PurchaseView.swift */; };
		588686332E162D0800A4C3FF /* SideMenuModifier.swift in Sources */ = {isa = PBXBuildFile; fileRef = 588685DD2E162D0800A4C3FF /* SideMenuModifier.swift */; };
		588686342E162D0800A4C3FF /* PricingView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 588685F52E162D0800A4C3FF /* PricingView.swift */; };
		588686352E162D0800A4C3FF /* ChatViewPreview.swift in Sources */ = {isa = PBXBuildFile; fileRef = 588685D82E162D0800A4C3FF /* ChatViewPreview.swift */; };
		588686362E162D0800A4C3FF /* ArchivedChatsListView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 588685CA2E162D0800A4C3FF /* ArchivedChatsListView.swift */; };
		588686372E162D0800A4C3FF /* StepProtocol.swift in Sources */ = {isa = PBXBuildFile; fileRef = 588686032E162D0800A4C3FF /* StepProtocol.swift */; };
		588686382E162D0800A4C3FF /* SettingsView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 588685F82E162D0800A4C3FF /* SettingsView.swift */; };
		588686392E162D0800A4C3FF /* MessageBubble.swift in Sources */ = {isa = PBXBuildFile; fileRef = 588685D92E162D0800A4C3FF /* MessageBubble.swift */; };
		5886863A2E162D0800A4C3FF /* InputField.swift in Sources */ = {isa = PBXBuildFile; fileRef = 588685E52E162D0800A4C3FF /* InputField.swift */; };
		5886863B2E162D0800A4C3FF /* MultiStepFormPreviewView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 588685FE2E162D0800A4C3FF /* MultiStepFormPreviewView.swift */; };
		5886863C2E162D0800A4C3FF /* ChatView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 588685D72E162D0800A4C3FF /* ChatView.swift */; };
		5886863D2E162D0800A4C3FF /* StepPreviewView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 588686022E162D0800A4C3FF /* StepPreviewView.swift */; };
		5886863E2E162D0800A4C3FF /* ScrollStateManager.swift in Sources */ = {isa = PBXBuildFile; fileRef = 588685DC2E162D0800A4C3FF /* ScrollStateManager.swift */; };
		5886863F2E162D0800A4C3FF /* PredefinedOptionKey.swift in Sources */ = {isa = PBXBuildFile; fileRef = 588686012E162D0800A4C3FF /* PredefinedOptionKey.swift */; };
		588686402E162D0800A4C3FF /* ScrollPositionReader.swift in Sources */ = {isa = PBXBuildFile; fileRef = 588685DB2E162D0800A4C3FF /* ScrollPositionReader.swift */; };
		588686412E162D0800A4C3FF /* PinCodeInputView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 588685EB2E162D0800A4C3FF /* PinCodeInputView.swift */; };
		588686422E162D0800A4C3FF /* ChatsListView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 588685D62E162D0800A4C3FF /* ChatsListView.swift */; };
		588686432E162D0800A4C3FF /* WebView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 588685F92E162D0800A4C3FF /* WebView.swift */; };
		588686442E162D0800A4C3FF /* RegisterView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 588685EC2E162D0800A4C3FF /* RegisterView.swift */; };
		588686452E162D0800A4C3FF /* ArchivedChatView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 588685CB2E162D0800A4C3FF /* ArchivedChatView.swift */; };
		588686462E162D0800A4C3FF /* AboutView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 588685F22E162D0800A4C3FF /* AboutView.swift */; };
		588686472E162D0800A4C3FF /* RecognizeFullScreenView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 588685D12E162D0800A4C3FF /* RecognizeFullScreenView.swift */; };
		588686482E162D0800A4C3FF /* RecognizeSmallView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 588685D32E162D0800A4C3FF /* RecognizeSmallView.swift */; };
		588686492E162D0800A4C3FF /* RechargeView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 588685F72E162D0800A4C3FF /* RechargeView.swift */; };
		5886864A2E162D0800A4C3FF /* SplashCodeSyntaxHighlighter.swift in Sources */ = {isa = PBXBuildFile; fileRef = 588685CE2E162D0800A4C3FF /* SplashCodeSyntaxHighlighter.swift */; };
		5886864B2E162D0800A4C3FF /* TypingIndicator.swift in Sources */ = {isa = PBXBuildFile; fileRef = 588685DF2E162D0800A4C3FF /* TypingIndicator.swift */; };
		5886864C2E162D0800A4C3FF /* StepView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 588686042E162D0800A4C3FF /* StepView.swift */; };
		5886864D2E162D0800A4C3FF /* MultiStepFormViewModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 588686002E162D0800A4C3FF /* MultiStepFormViewModel.swift */; };
		5886864E2E162D0800A4C3FF /* ArchivedChatViewModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 588685CC2E162D0800A4C3FF /* ArchivedChatViewModel.swift */; };
		5886864F2E162D0800A4C3FF /* AppListView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 588685F32E162D0800A4C3FF /* AppListView.swift */; };
		588686502E162D0800A4C3FF /* SplashView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 588685FB2E162D0800A4C3FF /* SplashView.swift */; };
		588686512E162D0800A4C3FF /* EmptySessionView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 588685E12E162D0800A4C3FF /* EmptySessionView.swift */; };
		588686522E162D0800A4C3FF /* TextOutputFormat.swift in Sources */ = {isa = PBXBuildFile; fileRef = 588685CF2E162D0800A4C3FF /* TextOutputFormat.swift */; };
		588686532E162D0800A4C3FF /* UpdatePromptView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 588686082E162D0800A4C3FF /* UpdatePromptView.swift */; };
		588686542E162D0800A4C3FF /* TextSelectionView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 588685DE2E162D0800A4C3FF /* TextSelectionView.swift */; };
		588686552E162D0800A4C3FF /* RecognizeView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 588685D42E162D0800A4C3FF /* RecognizeView.swift */; };
		588686562E162D0800A4C3FF /* InputViewModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 588685E62E162D0800A4C3FF /* InputViewModel.swift */; };
		588686572E162D0800A4C3FF /* LoginView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 588685EA2E162D0800A4C3FF /* LoginView.swift */; };
		588686582E162D0800A4C3FF /* EmailLoginView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 588685E82E162D0800A4C3FF /* EmailLoginView.swift */; };
		588686592E162D0800A4C3FF /* OptimizedMessageView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 588685DA2E162D0800A4C3FF /* OptimizedMessageView.swift */; };
		5886865A2E162D0800A4C3FF /* MultiStepFormView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 588685FF2E162D0800A4C3FF /* MultiStepFormView.swift */; };
		5886865B2E162D0800A4C3FF /* InputingAnimationView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 588685E92E162D0800A4C3FF /* InputingAnimationView.swift */; };
		5886865C2E162D0800A4C3FF /* EnhancedLoadingView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 588685E22E162D0800A4C3FF /* EnhancedLoadingView.swift */; };
		5886865D2E162D0800A4C3FF /* ChatConfigDatabaseManager.swift in Sources */ = {isa = PBXBuildFile; fileRef = 588685FD2E162D0800A4C3FF /* ChatConfigDatabaseManager.swift */; };
		5886865E2E162D0800A4C3FF /* SideMenu.swift in Sources */ = {isa = PBXBuildFile; fileRef = 588685F02E162D0800A4C3FF /* SideMenu.swift */; };
		5886865F2E162D0800A4C3FF /* StepViewModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 588686052E162D0800A4C3FF /* StepViewModel.swift */; };
		588686602E162D0800A4C3FF /* ContentView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 588685EF2E162D0800A4C3FF /* ContentView.swift */; };
		588686612E162D0800A4C3FF /* PreferLanguageView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 588685F42E162D0800A4C3FF /* PreferLanguageView.swift */; };
		588686622E162D0800A4C3FF /* RecognizeMessageView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 588685D22E162D0800A4C3FF /* RecognizeMessageView.swift */; };
		588686632E162D0800A4C3FF /* InputBottomView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 588685E42E162D0800A4C3FF /* InputBottomView.swift */; };
		588686642E162D0800A4C3FF /* VerificationView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 588685ED2E162D0800A4C3FF /* VerificationView.swift */; };
		588686652E162D0800A4C3FF /* MessageBubblePreview.swift in Sources */ = {isa = PBXBuildFile; fileRef = 588686072E162D0800A4C3FF /* MessageBubblePreview.swift */; };
		588686662E162D0800A4C3FF /* PurchaseView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 588685F62E162D0800A4C3FF /* PurchaseView.swift */; };
		588686672E162D0800A4C3FF /* SideMenuModifier.swift in Sources */ = {isa = PBXBuildFile; fileRef = 588685DD2E162D0800A4C3FF /* SideMenuModifier.swift */; };
		588686682E162D0800A4C3FF /* PricingView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 588685F52E162D0800A4C3FF /* PricingView.swift */; };
		588686692E162D0800A4C3FF /* ChatViewPreview.swift in Sources */ = {isa = PBXBuildFile; fileRef = 588685D82E162D0800A4C3FF /* ChatViewPreview.swift */; };
		5886866A2E162D0800A4C3FF /* ArchivedChatsListView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 588685CA2E162D0800A4C3FF /* ArchivedChatsListView.swift */; };
		5886866B2E162D0800A4C3FF /* StepProtocol.swift in Sources */ = {isa = PBXBuildFile; fileRef = 588686032E162D0800A4C3FF /* StepProtocol.swift */; };
		5886866C2E162D0800A4C3FF /* SettingsView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 588685F82E162D0800A4C3FF /* SettingsView.swift */; };
		5886866D2E162D0800A4C3FF /* MessageBubble.swift in Sources */ = {isa = PBXBuildFile; fileRef = 588685D92E162D0800A4C3FF /* MessageBubble.swift */; };
		5886866E2E162D0800A4C3FF /* InputField.swift in Sources */ = {isa = PBXBuildFile; fileRef = 588685E52E162D0800A4C3FF /* InputField.swift */; };
		5886866F2E162D0800A4C3FF /* MultiStepFormPreviewView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 588685FE2E162D0800A4C3FF /* MultiStepFormPreviewView.swift */; };
		588686702E162D0800A4C3FF /* ChatView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 588685D72E162D0800A4C3FF /* ChatView.swift */; };
		588686712E162D0800A4C3FF /* StepPreviewView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 588686022E162D0800A4C3FF /* StepPreviewView.swift */; };
		58DD65032E404CB1007F49EC /* AppStartupManager.swift in Sources */ = {isa = PBXBuildFile; fileRef = 58DD65022E404CB1007F49EC /* AppStartupManager.swift */; };
		58DD65042E404CB1007F49EC /* AppStartupManager.swift in Sources */ = {isa = PBXBuildFile; fileRef = 58DD65022E404CB1007F49EC /* AppStartupManager.swift */; };
		58DD65072E404CBE007F49EC /* StartupLoadingView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 58DD65052E404CBE007F49EC /* StartupLoadingView.swift */; };
		58DD65082E404CBE007F49EC /* StartupLoadingView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 58DD65052E404CBE007F49EC /* StartupLoadingView.swift */; };
		58DD651B2E405271007F49EC /* OptimizedChatRepository.swift in Sources */ = {isa = PBXBuildFile; fileRef = 58DD65122E405271007F49EC /* OptimizedChatRepository.swift */; };
		58DD651E2E405271007F49EC /* OptimizedMessageRepository.swift in Sources */ = {isa = PBXBuildFile; fileRef = 58DD65132E405271007F49EC /* OptimizedMessageRepository.swift */; };
		58DD65202E405271007F49EC /* OptimizedChatRepository.swift in Sources */ = {isa = PBXBuildFile; fileRef = 58DD65122E405271007F49EC /* OptimizedChatRepository.swift */; };
		58DD65232E405271007F49EC /* OptimizedMessageRepository.swift in Sources */ = {isa = PBXBuildFile; fileRef = 58DD65132E405271007F49EC /* OptimizedMessageRepository.swift */; };
		58DD65262E405289007F49EC /* OptimizedChatRowView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 58DD65242E405289007F49EC /* OptimizedChatRowView.swift */; };
		58DD65272E405289007F49EC /* OptimizedChatRowView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 58DD65242E405289007F49EC /* OptimizedChatRowView.swift */; };
		58DD65292E405789007F49EC /* MessageRepositoryProtocol.swift in Sources */ = {isa = PBXBuildFile; fileRef = 58DD65282E405789007F49EC /* MessageRepositoryProtocol.swift */; };
		58DD652A2E405789007F49EC /* MessageRepositoryProtocol.swift in Sources */ = {isa = PBXBuildFile; fileRef = 58DD65282E405789007F49EC /* MessageRepositoryProtocol.swift */; };
		58DD652C2E405E76007F49EC /* APIRequestManager.swift in Sources */ = {isa = PBXBuildFile; fileRef = 58DD652B2E405E76007F49EC /* APIRequestManager.swift */; };
		58DD652D2E405E76007F49EC /* APIRequestManager.swift in Sources */ = {isa = PBXBuildFile; fileRef = 58DD652B2E405E76007F49EC /* APIRequestManager.swift */; };
		58DD65342E4097C4007F49EC /* UnifiedStepFormView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 58DD65332E4097C4007F49EC /* UnifiedStepFormView.swift */; };
		58DD65362E4097C4007F49EC /* StepEditSection.swift in Sources */ = {isa = PBXBuildFile; fileRef = 58DD65322E4097C4007F49EC /* StepEditSection.swift */; };
		58DD65372E4097C4007F49EC /* UnifiedStepFormView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 58DD65332E4097C4007F49EC /* UnifiedStepFormView.swift */; };
		58DD65392E4097C4007F49EC /* StepEditSection.swift in Sources */ = {isa = PBXBuildFile; fileRef = 58DD65322E4097C4007F49EC /* StepEditSection.swift */; };
		58EDE1DA2E38A40F00AB1BA8 /* DataChangeNotificationCenter.swift in Sources */ = {isa = PBXBuildFile; fileRef = 58EDE1BB2E38A40F00AB1BA8 /* DataChangeNotificationCenter.swift */; };
		58EDE1DC2E38A40F00AB1BA8 /* DataFlowDebugger.swift in Sources */ = {isa = PBXBuildFile; fileRef = 58EDE1BE2E38A40F00AB1BA8 /* DataFlowDebugger.swift */; };
		58EDE1DD2E38A40F00AB1BA8 /* AccountManager.swift in Sources */ = {isa = PBXBuildFile; fileRef = 58EDE1D02E38A40F00AB1BA8 /* AccountManager.swift */; };
		58EDE1DE2E38A40F00AB1BA8 /* NetworkStatusManager.swift in Sources */ = {isa = PBXBuildFile; fileRef = 58EDE1C92E38A40F00AB1BA8 /* NetworkStatusManager.swift */; };
		58EDE1DF2E38A40F00AB1BA8 /* ChatTarget.swift in Sources */ = {isa = PBXBuildFile; fileRef = 58EDE1C42E38A40F00AB1BA8 /* ChatTarget.swift */; };
		58EDE1E02E38A40F00AB1BA8 /* VersionUpdateManager.swift in Sources */ = {isa = PBXBuildFile; fileRef = 58EDE1D82E38A40F00AB1BA8 /* VersionUpdateManager.swift */; };
		58EDE1E12E38A40F00AB1BA8 /* ChatListRepository.swift in Sources */ = {isa = PBXBuildFile; fileRef = 58EDE1B32E38A40F00AB1BA8 /* ChatListRepository.swift */; };
		58EDE1E22E38A40F00AB1BA8 /* APICommom.swift in Sources */ = {isa = PBXBuildFile; fileRef = 58EDE1C22E38A40F00AB1BA8 /* APICommom.swift */; };
		58EDE1E32E38A40F00AB1BA8 /* CacheManager.swift in Sources */ = {isa = PBXBuildFile; fileRef = 58EDE1AF2E38A40F00AB1BA8 /* CacheManager.swift */; };
		58EDE1E42E38A40F00AB1BA8 /* TypewriterEffectManager.swift in Sources */ = {isa = PBXBuildFile; fileRef = 58EDE1D62E38A40F00AB1BA8 /* TypewriterEffectManager.swift */; };
		58EDE1E52E38A40F00AB1BA8 /* DatabaseVersion.swift in Sources */ = {isa = PBXBuildFile; fileRef = 58EDE1BA2E38A40F00AB1BA8 /* DatabaseVersion.swift */; };
		58EDE1E62E38A40F00AB1BA8 /* ChatRepositoryProtocol.swift in Sources */ = {isa = PBXBuildFile; fileRef = 58EDE1B52E38A40F00AB1BA8 /* ChatRepositoryProtocol.swift */; };
		58EDE1E72E38A40F00AB1BA8 /* ConnectionManager.swift in Sources */ = {isa = PBXBuildFile; fileRef = 58EDE1C62E38A40F00AB1BA8 /* ConnectionManager.swift */; };
		58EDE1E82E38A40F00AB1BA8 /* DatabaseIntegrityChecker.swift in Sources */ = {isa = PBXBuildFile; fileRef = 58EDE1B92E38A40F00AB1BA8 /* DatabaseIntegrityChecker.swift */; };
		58EDE1E92E38A40F00AB1BA8 /* MessageLoader.swift in Sources */ = {isa = PBXBuildFile; fileRef = 58EDE1D32E38A40F00AB1BA8 /* MessageLoader.swift */; };
		58EDE1EA2E38A40F00AB1BA8 /* UserSettings.swift in Sources */ = {isa = PBXBuildFile; fileRef = 58EDE1D72E38A40F00AB1BA8 /* UserSettings.swift */; };
		58EDE1EB2E38A40F00AB1BA8 /* MessageRepository.swift in Sources */ = {isa = PBXBuildFile; fileRef = 58EDE1B62E38A40F00AB1BA8 /* MessageRepository.swift */; };
		58EDE1EC2E38A40F00AB1BA8 /* NetworkService.swift in Sources */ = {isa = PBXBuildFile; fileRef = 58EDE1C82E38A40F00AB1BA8 /* NetworkService.swift */; };
		58EDE1ED2E38A40F00AB1BA8 /* PricingTarget.swift in Sources */ = {isa = PBXBuildFile; fileRef = 58EDE1CA2E38A40F00AB1BA8 /* PricingTarget.swift */; };
		58EDE1EF2E38A40F00AB1BA8 /* ChatRepository.swift in Sources */ = {isa = PBXBuildFile; fileRef = 58EDE1B42E38A40F00AB1BA8 /* ChatRepository.swift */; };
		58EDE1F02E38A40F00AB1BA8 /* ChatSessionManager.swift in Sources */ = {isa = PBXBuildFile; fileRef = 58EDE1D22E38A40F00AB1BA8 /* ChatSessionManager.swift */; };
		58EDE1F12E38A40F00AB1BA8 /* ConfigTarget.swift in Sources */ = {isa = PBXBuildFile; fileRef = 58EDE1C52E38A40F00AB1BA8 /* ConfigTarget.swift */; };
		58EDE1F22E38A40F00AB1BA8 /* AuthTarget.swift in Sources */ = {isa = PBXBuildFile; fileRef = 58EDE1C32E38A40F00AB1BA8 /* AuthTarget.swift */; };
		58EDE1F32E38A40F00AB1BA8 /* BootsManager.swift in Sources */ = {isa = PBXBuildFile; fileRef = 58EDE1D12E38A40F00AB1BA8 /* BootsManager.swift */; };
		58EDE1F42E38A40F00AB1BA8 /* ChatListComponentFactory.swift in Sources */ = {isa = PBXBuildFile; fileRef = 58EDE1C02E38A40F00AB1BA8 /* ChatListComponentFactory.swift */; };
		58EDE1F52E38A40F00AB1BA8 /* PurchaseTarget.swift in Sources */ = {isa = PBXBuildFile; fileRef = 58EDE1CB2E38A40F00AB1BA8 /* PurchaseTarget.swift */; };
		58EDE1F62E38A40F00AB1BA8 /* UnifiedSSEManager.swift in Sources */ = {isa = PBXBuildFile; fileRef = 58EDE1CC2E38A40F00AB1BA8 /* UnifiedSSEManager.swift */; };
		58EDE1F72E38A40F00AB1BA8 /* Preferences.swift in Sources */ = {isa = PBXBuildFile; fileRef = 58EDE1D42E38A40F00AB1BA8 /* Preferences.swift */; };
		58EDE1F82E38A40F00AB1BA8 /* AdvisorDatabaseManager.swift in Sources */ = {isa = PBXBuildFile; fileRef = 58EDE1B82E38A40F00AB1BA8 /* AdvisorDatabaseManager.swift */; };
		58EDE1F92E38A40F00AB1BA8 /* ShopManager.swift in Sources */ = {isa = PBXBuildFile; fileRef = 58EDE1D52E38A40F00AB1BA8 /* ShopManager.swift */; };
		58EDE1FA2E38A40F00AB1BA8 /* BaseMigration.swift in Sources */ = {isa = PBXBuildFile; fileRef = 58EDE1B12E38A40F00AB1BA8 /* BaseMigration.swift */; };
		58EDE1FB2E38A40F00AB1BA8 /* ErrorRecoveryManager.swift in Sources */ = {isa = PBXBuildFile; fileRef = 58EDE1C72E38A40F00AB1BA8 /* ErrorRecoveryManager.swift */; };
		58EDE1FC2E38A40F00AB1BA8 /* DataChangeNotificationCenter.swift in Sources */ = {isa = PBXBuildFile; fileRef = 58EDE1BB2E38A40F00AB1BA8 /* DataChangeNotificationCenter.swift */; };
		58EDE1FE2E38A40F00AB1BA8 /* DataFlowDebugger.swift in Sources */ = {isa = PBXBuildFile; fileRef = 58EDE1BE2E38A40F00AB1BA8 /* DataFlowDebugger.swift */; };
		58EDE1FF2E38A40F00AB1BA8 /* AccountManager.swift in Sources */ = {isa = PBXBuildFile; fileRef = 58EDE1D02E38A40F00AB1BA8 /* AccountManager.swift */; };
		58EDE2002E38A40F00AB1BA8 /* NetworkStatusManager.swift in Sources */ = {isa = PBXBuildFile; fileRef = 58EDE1C92E38A40F00AB1BA8 /* NetworkStatusManager.swift */; };
		58EDE2012E38A40F00AB1BA8 /* ChatTarget.swift in Sources */ = {isa = PBXBuildFile; fileRef = 58EDE1C42E38A40F00AB1BA8 /* ChatTarget.swift */; };
		58EDE2022E38A40F00AB1BA8 /* VersionUpdateManager.swift in Sources */ = {isa = PBXBuildFile; fileRef = 58EDE1D82E38A40F00AB1BA8 /* VersionUpdateManager.swift */; };
		58EDE2032E38A40F00AB1BA8 /* ChatListRepository.swift in Sources */ = {isa = PBXBuildFile; fileRef = 58EDE1B32E38A40F00AB1BA8 /* ChatListRepository.swift */; };
		58EDE2042E38A40F00AB1BA8 /* APICommom.swift in Sources */ = {isa = PBXBuildFile; fileRef = 58EDE1C22E38A40F00AB1BA8 /* APICommom.swift */; };
		58EDE2052E38A40F00AB1BA8 /* CacheManager.swift in Sources */ = {isa = PBXBuildFile; fileRef = 58EDE1AF2E38A40F00AB1BA8 /* CacheManager.swift */; };
		58EDE2062E38A40F00AB1BA8 /* TypewriterEffectManager.swift in Sources */ = {isa = PBXBuildFile; fileRef = 58EDE1D62E38A40F00AB1BA8 /* TypewriterEffectManager.swift */; };
		58EDE2072E38A40F00AB1BA8 /* DatabaseVersion.swift in Sources */ = {isa = PBXBuildFile; fileRef = 58EDE1BA2E38A40F00AB1BA8 /* DatabaseVersion.swift */; };
		58EDE2082E38A40F00AB1BA8 /* ChatRepositoryProtocol.swift in Sources */ = {isa = PBXBuildFile; fileRef = 58EDE1B52E38A40F00AB1BA8 /* ChatRepositoryProtocol.swift */; };
		58EDE2092E38A40F00AB1BA8 /* ConnectionManager.swift in Sources */ = {isa = PBXBuildFile; fileRef = 58EDE1C62E38A40F00AB1BA8 /* ConnectionManager.swift */; };
		58EDE20A2E38A40F00AB1BA8 /* DatabaseIntegrityChecker.swift in Sources */ = {isa = PBXBuildFile; fileRef = 58EDE1B92E38A40F00AB1BA8 /* DatabaseIntegrityChecker.swift */; };
		58EDE20B2E38A40F00AB1BA8 /* MessageLoader.swift in Sources */ = {isa = PBXBuildFile; fileRef = 58EDE1D32E38A40F00AB1BA8 /* MessageLoader.swift */; };
		58EDE20C2E38A40F00AB1BA8 /* UserSettings.swift in Sources */ = {isa = PBXBuildFile; fileRef = 58EDE1D72E38A40F00AB1BA8 /* UserSettings.swift */; };
		58EDE20D2E38A40F00AB1BA8 /* MessageRepository.swift in Sources */ = {isa = PBXBuildFile; fileRef = 58EDE1B62E38A40F00AB1BA8 /* MessageRepository.swift */; };
		58EDE20E2E38A40F00AB1BA8 /* NetworkService.swift in Sources */ = {isa = PBXBuildFile; fileRef = 58EDE1C82E38A40F00AB1BA8 /* NetworkService.swift */; };
		58EDE20F2E38A40F00AB1BA8 /* PricingTarget.swift in Sources */ = {isa = PBXBuildFile; fileRef = 58EDE1CA2E38A40F00AB1BA8 /* PricingTarget.swift */; };
		58EDE2112E38A40F00AB1BA8 /* ChatRepository.swift in Sources */ = {isa = PBXBuildFile; fileRef = 58EDE1B42E38A40F00AB1BA8 /* ChatRepository.swift */; };
		58EDE2122E38A40F00AB1BA8 /* ChatSessionManager.swift in Sources */ = {isa = PBXBuildFile; fileRef = 58EDE1D22E38A40F00AB1BA8 /* ChatSessionManager.swift */; };
		58EDE2132E38A40F00AB1BA8 /* ConfigTarget.swift in Sources */ = {isa = PBXBuildFile; fileRef = 58EDE1C52E38A40F00AB1BA8 /* ConfigTarget.swift */; };
		58EDE2142E38A40F00AB1BA8 /* AuthTarget.swift in Sources */ = {isa = PBXBuildFile; fileRef = 58EDE1C32E38A40F00AB1BA8 /* AuthTarget.swift */; };
		58EDE2152E38A40F00AB1BA8 /* BootsManager.swift in Sources */ = {isa = PBXBuildFile; fileRef = 58EDE1D12E38A40F00AB1BA8 /* BootsManager.swift */; };
		58EDE2162E38A40F00AB1BA8 /* ChatListComponentFactory.swift in Sources */ = {isa = PBXBuildFile; fileRef = 58EDE1C02E38A40F00AB1BA8 /* ChatListComponentFactory.swift */; };
		58EDE2172E38A40F00AB1BA8 /* PurchaseTarget.swift in Sources */ = {isa = PBXBuildFile; fileRef = 58EDE1CB2E38A40F00AB1BA8 /* PurchaseTarget.swift */; };
		58EDE2182E38A40F00AB1BA8 /* UnifiedSSEManager.swift in Sources */ = {isa = PBXBuildFile; fileRef = 58EDE1CC2E38A40F00AB1BA8 /* UnifiedSSEManager.swift */; };
		58EDE2192E38A40F00AB1BA8 /* Preferences.swift in Sources */ = {isa = PBXBuildFile; fileRef = 58EDE1D42E38A40F00AB1BA8 /* Preferences.swift */; };
		58EDE21A2E38A40F00AB1BA8 /* AdvisorDatabaseManager.swift in Sources */ = {isa = PBXBuildFile; fileRef = 58EDE1B82E38A40F00AB1BA8 /* AdvisorDatabaseManager.swift */; };
		58EDE21B2E38A40F00AB1BA8 /* ShopManager.swift in Sources */ = {isa = PBXBuildFile; fileRef = 58EDE1D52E38A40F00AB1BA8 /* ShopManager.swift */; };
		58EDE21C2E38A40F00AB1BA8 /* BaseMigration.swift in Sources */ = {isa = PBXBuildFile; fileRef = 58EDE1B12E38A40F00AB1BA8 /* BaseMigration.swift */; };
		58EDE21D2E38A40F00AB1BA8 /* ErrorRecoveryManager.swift in Sources */ = {isa = PBXBuildFile; fileRef = 58EDE1C72E38A40F00AB1BA8 /* ErrorRecoveryManager.swift */; };
		58EDE2312E38A42C00AB1BA8 /* ChatListUIStateManager.swift in Sources */ = {isa = PBXBuildFile; fileRef = 58EDE2232E38A42C00AB1BA8 /* ChatListUIStateManager.swift */; };
		58EDE2322E38A42C00AB1BA8 /* ContentViewModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 58EDE2292E38A42C00AB1BA8 /* ContentViewModel.swift */; };
		58EDE2332E38A42C00AB1BA8 /* AuthViewModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 58EDE2262E38A42C00AB1BA8 /* AuthViewModel.swift */; };
		58EDE2342E38A42C00AB1BA8 /* SplashViewModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 58EDE22E2E38A42C00AB1BA8 /* SplashViewModel.swift */; };
		58EDE2352E38A42C00AB1BA8 /* ChatViewModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 58EDE2282E38A42C00AB1BA8 /* ChatViewModel.swift */; };
		58EDE2362E38A42C00AB1BA8 /* ChatListCoordinator.swift in Sources */ = {isa = PBXBuildFile; fileRef = 58EDE2212E38A42C00AB1BA8 /* ChatListCoordinator.swift */; };
		58EDE2372E38A42C00AB1BA8 /* ChatListViewModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 58EDE2272E38A42C00AB1BA8 /* ChatListViewModel.swift */; };
		58EDE2382E38A42C00AB1BA8 /* VerificationViewModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 58EDE22F2E38A42C00AB1BA8 /* VerificationViewModel.swift */; };
		58EDE2392E38A42C00AB1BA8 /* PricingViewModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 58EDE22A2E38A42C00AB1BA8 /* PricingViewModel.swift */; };
		58EDE23A2E38A42C00AB1BA8 /* RecognizeViewModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 58EDE22C2E38A42C00AB1BA8 /* RecognizeViewModel.swift */; };
		58EDE23B2E38A42C00AB1BA8 /* ChatListDataSource.swift in Sources */ = {isa = PBXBuildFile; fileRef = 58EDE2222E38A42C00AB1BA8 /* ChatListDataSource.swift */; };
		58EDE23C2E38A42C00AB1BA8 /* RegisterViewModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 58EDE22D2E38A42C00AB1BA8 /* RegisterViewModel.swift */; };
		58EDE23D2E38A42C00AB1BA8 /* MessageManager.swift in Sources */ = {isa = PBXBuildFile; fileRef = 58EDE21F2E38A42C00AB1BA8 /* MessageManager.swift */; };
		58EDE23E2E38A42C00AB1BA8 /* ChatUIStateManager.swift in Sources */ = {isa = PBXBuildFile; fileRef = 58EDE21E2E38A42C00AB1BA8 /* ChatUIStateManager.swift */; };
		58EDE23F2E38A42C00AB1BA8 /* RefactoredChatListViewModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 58EDE2242E38A42C00AB1BA8 /* RefactoredChatListViewModel.swift */; };
		58EDE2402E38A42C00AB1BA8 /* PurchaseViewModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 58EDE22B2E38A42C00AB1BA8 /* PurchaseViewModel.swift */; };
		58EDE2412E38A42C00AB1BA8 /* ChatListUIStateManager.swift in Sources */ = {isa = PBXBuildFile; fileRef = 58EDE2232E38A42C00AB1BA8 /* ChatListUIStateManager.swift */; };
		58EDE2422E38A42C00AB1BA8 /* ContentViewModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 58EDE2292E38A42C00AB1BA8 /* ContentViewModel.swift */; };
		58EDE2432E38A42C00AB1BA8 /* AuthViewModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 58EDE2262E38A42C00AB1BA8 /* AuthViewModel.swift */; };
		58EDE2442E38A42C00AB1BA8 /* SplashViewModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 58EDE22E2E38A42C00AB1BA8 /* SplashViewModel.swift */; };
		58EDE2452E38A42C00AB1BA8 /* ChatViewModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 58EDE2282E38A42C00AB1BA8 /* ChatViewModel.swift */; };
		58EDE2462E38A42C00AB1BA8 /* ChatListCoordinator.swift in Sources */ = {isa = PBXBuildFile; fileRef = 58EDE2212E38A42C00AB1BA8 /* ChatListCoordinator.swift */; };
		58EDE2472E38A42C00AB1BA8 /* ChatListViewModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 58EDE2272E38A42C00AB1BA8 /* ChatListViewModel.swift */; };
		58EDE2482E38A42C00AB1BA8 /* VerificationViewModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 58EDE22F2E38A42C00AB1BA8 /* VerificationViewModel.swift */; };
		58EDE2492E38A42C00AB1BA8 /* PricingViewModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 58EDE22A2E38A42C00AB1BA8 /* PricingViewModel.swift */; };
		58EDE24A2E38A42C00AB1BA8 /* RecognizeViewModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 58EDE22C2E38A42C00AB1BA8 /* RecognizeViewModel.swift */; };
		58EDE24B2E38A42C00AB1BA8 /* ChatListDataSource.swift in Sources */ = {isa = PBXBuildFile; fileRef = 58EDE2222E38A42C00AB1BA8 /* ChatListDataSource.swift */; };
		58EDE24C2E38A42C00AB1BA8 /* RegisterViewModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 58EDE22D2E38A42C00AB1BA8 /* RegisterViewModel.swift */; };
		58EDE24D2E38A42C00AB1BA8 /* MessageManager.swift in Sources */ = {isa = PBXBuildFile; fileRef = 58EDE21F2E38A42C00AB1BA8 /* MessageManager.swift */; };
		58EDE24E2E38A42C00AB1BA8 /* ChatUIStateManager.swift in Sources */ = {isa = PBXBuildFile; fileRef = 58EDE21E2E38A42C00AB1BA8 /* ChatUIStateManager.swift */; };
		58EDE24F2E38A42C00AB1BA8 /* RefactoredChatListViewModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 58EDE2242E38A42C00AB1BA8 /* RefactoredChatListViewModel.swift */; };
		58EDE2502E38A42C00AB1BA8 /* PurchaseViewModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 58EDE22B2E38A42C00AB1BA8 /* PurchaseViewModel.swift */; };
		6C0310792CDC6DDF00E0C305 /* AppDelegate.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6C0310782CDC6DDF00E0C305 /* AppDelegate.swift */; };
		6C0CBB0F2BF756DB009036A5 /* WeakList.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6C0CBB0E2BF756DB009036A5 /* WeakList.swift */; };
		6C17C4DC2C083970005BA6A3 /* VibrationController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6C17C4DB2C083970005BA6A3 /* VibrationController.swift */; };
		6C17C4E62C09740F005BA6A3 /* Info.plist in Resources */ = {isa = PBXBuildFile; fileRef = 6C17C4E12C09715B005BA6A3 /* Info.plist */; };
		6C17C4E82C09BDAD005BA6A3 /* Product.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6C17C4E72C09BDAD005BA6A3 /* Product.swift */; };
		6C18B8382CC5EF660029CB87 /* AdSupport.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 6C18B8372CC5EF660029CB87 /* AdSupport.framework */; platformFilter = ios; };
		6C1FCBAE2C5724E400706697 /* URLExtension.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6C1FCBAD2C5724E400706697 /* URLExtension.swift */; };
		6C3000D52BDF70DE0079AF38 /* OpenAIModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6C3000D42BDF70DE0079AF38 /* OpenAIModel.swift */; };
		6C3987382BEB26CF0026E28C /* LDSwiftEventSource in Frameworks */ = {isa = PBXBuildFile; productRef = 6C3987372BEB26CF0026E28C /* LDSwiftEventSource */; };
		6C4529D12C52262100489F07 /* FacebookCore in Frameworks */ = {isa = PBXBuildFile; productRef = 6C4529D02C52262100489F07 /* FacebookCore */; };
		6C4529D32C52262100489F07 /* FacebookLogin in Frameworks */ = {isa = PBXBuildFile; productRef = 6C4529D22C52262100489F07 /* FacebookLogin */; };
		6C62A6C22C465639002D40D5 /* AppReviewManager.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6C62A6C12C465639002D40D5 /* AppReviewManager.swift */; };
		6C62A6C52C48C7AC002D40D5 /* EventSource in Frameworks */ = {isa = PBXBuildFile; productRef = 6C62A6C42C48C7AC002D40D5 /* EventSource */; };
		6C6690BC2C2EDA6B00FA97E1 /* ColorExtension.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6C6690BB2C2EDA6B00FA97E1 /* ColorExtension.swift */; };
		6C6690BE2C2EDA9500FA97E1 /* UserDefaultsExtension.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6C6690BD2C2EDA9500FA97E1 /* UserDefaultsExtension.swift */; };
		6C6690C02C2EE46000FA97E1 /* FirebaseManager.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6C6690BF2C2EE46000FA97E1 /* FirebaseManager.swift */; };
		6C6E214D2C33B2170081A7D6 /* FirebaseMessaging in Frameworks */ = {isa = PBXBuildFile; productRef = 6C6E214C2C33B2170081A7D6 /* FirebaseMessaging */; };
		6C6E215E2C364B1D0081A7D6 /* RecognizedText.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6C6E215D2C364B1D0081A7D6 /* RecognizedText.swift */; };
		6C71C88D2C3CCD020001B917 /* StepperView in Frameworks */ = {isa = PBXBuildFile; productRef = 6C71C88C2C3CCD020001B917 /* StepperView */; };
		6C90D6962CE763A600AD016E /* TikTokOpenSDKCore in Frameworks */ = {isa = PBXBuildFile; productRef = 6C90D6952CE763A600AD016E /* TikTokOpenSDKCore */; };
		6C90D6982CE7645300AD016E /* TikTokOpenAuthSDK in Frameworks */ = {isa = PBXBuildFile; productRef = 6C90D6972CE7645300AD016E /* TikTokOpenAuthSDK */; };
		6C9739612BDE2DFB00F53F1D /* LookinServer in Frameworks */ = {isa = PBXBuildFile; productRef = 6C9739602BDE2DFB00F53F1D /* LookinServer */; };
		6C9739632BDE2E5900F53F1D /* AppThemes.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6C9739622BDE2E5900F53F1D /* AppThemes.swift */; };
		6CA0CEA82BD24B8600A6B32B /* StringExtension.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6CA0CEA72BD24B8600A6B32B /* StringExtension.swift */; };
		6CA0CEAB2BD24C1C00A6B32B /* SwifterSwift in Frameworks */ = {isa = PBXBuildFile; productRef = 6CA0CEAA2BD24C1C00A6B32B /* SwifterSwift */; };
		6CA0CEAD2BD251EB00A6B32B /* ViewExtension.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6CA0CEAC2BD251EB00A6B32B /* ViewExtension.swift */; };
		6CA36DE22C1833EB00AE82B2 /* InfoPlist.strings in Resources */ = {isa = PBXBuildFile; fileRef = 6CA36DE02C1833EB00AE82B2 /* InfoPlist.strings */; };
		6CC844282BD2096A00B4698C /* KeychainSwift in Frameworks */ = {isa = PBXBuildFile; productRef = 6CC844272BD2096A00B4698C /* KeychainSwift */; };
		6CC844332BD21B8800B4698C /* Localize_Swift in Frameworks */ = {isa = PBXBuildFile; productRef = 6CC844322BD21B8800B4698C /* Localize_Swift */; };
		6CD675312BF5EB58008134B8 /* Splash in Frameworks */ = {isa = PBXBuildFile; productRef = 6CD675302BF5EB58008134B8 /* Splash */; };
		6CDA93F82BF483EA00C52DDF /* MarkdownUI in Frameworks */ = {isa = PBXBuildFile; productRef = 6CDA93F72BF483EA00C52DDF /* MarkdownUI */; };
		6CE61D472BEC7D8B00B8B4F1 /* DateExtension .swift in Sources */ = {isa = PBXBuildFile; fileRef = 6CE61D462BEC7D8B00B8B4F1 /* DateExtension .swift */; };
		6CE61D682BEF02D400B8B4F1 /* SimpleToast in Frameworks */ = {isa = PBXBuildFile; productRef = 6CE61D672BEF02D400B8B4F1 /* SimpleToast */; };
		6CE61D722BF3041A00B8B4F1 /* PrivacyInfo.xcprivacy in Resources */ = {isa = PBXBuildFile; fileRef = 6CE61D712BF3041900B8B4F1 /* PrivacyInfo.xcprivacy */; };
		6CFEB0002BE0DFD70046C53A /* WCDBSwift in Frameworks */ = {isa = PBXBuildFile; productRef = 6CFEAFFF2BE0DFD70046C53A /* WCDBSwift */; };
		9706CC6A2BC3CFA40080FECE /* ChatAdvisorApp.swift in Sources */ = {isa = PBXBuildFile; fileRef = 9706CC692BC3CFA40080FECE /* ChatAdvisorApp.swift */; };
		9706CC6E2BC3CFA80080FECE /* Assets.xcassets in Resources */ = {isa = PBXBuildFile; fileRef = 9706CC6D2BC3CFA80080FECE /* Assets.xcassets */; };
		9706CC712BC3CFA80080FECE /* Preview Assets.xcassets in Resources */ = {isa = PBXBuildFile; fileRef = 9706CC702BC3CFA80080FECE /* Preview Assets.xcassets */; };
		9706CC7B2BC3CFA80080FECE /* JunShiTests.swift in Sources */ = {isa = PBXBuildFile; fileRef = 9706CC7A2BC3CFA80080FECE /* JunShiTests.swift */; };
		9706CC852BC3CFA90080FECE /* JunShiUITests.swift in Sources */ = {isa = PBXBuildFile; fileRef = 9706CC842BC3CFA90080FECE /* JunShiUITests.swift */; };
		9706CC872BC3CFA90080FECE /* JunShiUITestsLaunchTests.swift in Sources */ = {isa = PBXBuildFile; fileRef = 9706CC862BC3CFA90080FECE /* JunShiUITestsLaunchTests.swift */; };
		9706CC9E2BC3ECB50080FECE /* FirebaseAnalytics in Frameworks */ = {isa = PBXBuildFile; productRef = 9706CC9D2BC3ECB50080FECE /* FirebaseAnalytics */; };
		9706CCAA2BC3F2730080FECE /* ChatAdvisorApp.swift in Sources */ = {isa = PBXBuildFile; fileRef = 9706CC692BC3CFA40080FECE /* ChatAdvisorApp.swift */; };
		9706CCAC2BC3F2730080FECE /* JunShiPackage in Frameworks */ = {isa = PBXBuildFile; productRef = 9706CCA52BC3F2730080FECE /* JunShiPackage */; };
		9706CCAD2BC3F2730080FECE /* JunShiPackage in Frameworks */ = {isa = PBXBuildFile; productRef = 9706CCA42BC3F2730080FECE /* JunShiPackage */; };
		9706CCAE2BC3F2730080FECE /* FirebaseAnalytics in Frameworks */ = {isa = PBXBuildFile; productRef = 9706CCA62BC3F2730080FECE /* FirebaseAnalytics */; };
		9706CCB02BC3F2730080FECE /* Preview Assets.xcassets in Resources */ = {isa = PBXBuildFile; fileRef = 9706CC702BC3CFA80080FECE /* Preview Assets.xcassets */; };
		9706CCB12BC3F2730080FECE /* Assets.xcassets in Resources */ = {isa = PBXBuildFile; fileRef = 9706CC6D2BC3CFA80080FECE /* Assets.xcassets */; };
		9706CCBC2BC4018A0080FECE /* GoogleService-Info.plist in Resources */ = {isa = PBXBuildFile; fileRef = 9706CC9F2BC3ED460080FECE /* GoogleService-Info.plist */; };
		9777D1052BC7DAEE00619386 /* Moya in Frameworks */ = {isa = PBXBuildFile; productRef = 9777D1042BC7DAEE00619386 /* Moya */; };
		9777D1082BC7DB3200619386 /* Alamofire in Frameworks */ = {isa = PBXBuildFile; productRef = 9777D1072BC7DB3200619386 /* Alamofire */; };
		D61300662BF3B561006C86C0 /* ChatsModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = D61300652BF3B561006C86C0 /* ChatsModel.swift */; };
		D62099612C0B8731000178CF /* Configs.swift in Sources */ = {isa = PBXBuildFile; fileRef = D62099602C0B8731000178CF /* Configs.swift */; };
		D64340272BF8C33B00F62E35 /* Balance.swift in Sources */ = {isa = PBXBuildFile; fileRef = D64340262BF8C33B00F62E35 /* Balance.swift */; };
		D643402C2BF8D22000F62E35 /* StoreKit.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = D643402B2BF8D22000F62E35 /* StoreKit.framework */; };
		D65D5CD92C232D14004504BE /* Bugly.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = D65D5CD82C232D14004504BE /* Bugly.framework */; };
		D65D5CDB2C232D2B004504BE /* SystemConfiguration.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = D65D5CDA2C232D2B004504BE /* SystemConfiguration.framework */; };
		D65D5CDD2C232D34004504BE /* Security.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = D65D5CDC2C232D34004504BE /* Security.framework */; };
		D66B674F2C2B03EB00DB7598 /* GoogleSignInSwift in Frameworks */ = {isa = PBXBuildFile; productRef = D66B674E2C2B03EB00DB7598 /* GoogleSignInSwift */; };
		D6C63CB22BCD6A420004BF5E /* CryptoFunction.swift in Sources */ = {isa = PBXBuildFile; fileRef = D6C63C9D2BCD6A420004BF5E /* CryptoFunction.swift */; };
		D6C63CBC2BCD6A420004BF5E /* Chats.swift in Sources */ = {isa = PBXBuildFile; fileRef = D6C63CAE2BCD6A420004BF5E /* Chats.swift */; };
		D6C63CBD2BCD6A420004BF5E /* NetworkResponse.swift in Sources */ = {isa = PBXBuildFile; fileRef = D6C63CAF2BCD6A420004BF5E /* NetworkResponse.swift */; };
		D6C63CBE2BCD6A420004BF5E /* User.swift in Sources */ = {isa = PBXBuildFile; fileRef = D6C63CB02BCD6A420004BF5E /* User.swift */; };
		D6C84FE62BC436D0005FBCD6 /* FirebaseCrashlytics in Frameworks */ = {isa = PBXBuildFile; productRef = D6C84FE52BC436D0005FBCD6 /* FirebaseCrashlytics */; };
		D6DC913F2C0C6C1F009DB0AB /* Localizable.strings in Resources */ = {isa = PBXBuildFile; fileRef = D6DC913D2C0C6C1F009DB0AB /* Localizable.strings */; };
		D6DC914B2C1205B3009DB0AB /* UIDeviceExtension.swift in Sources */ = {isa = PBXBuildFile; fileRef = D6DC914A2C1205B3009DB0AB /* UIDeviceExtension.swift */; };
/* End PBXBuildFile section */

/* Begin PBXContainerItemProxy section */
		9706CC772BC3CFA80080FECE /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 9706CC5E2BC3CFA40080FECE /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 9706CC652BC3CFA40080FECE;
			remoteInfo = JunShi;
		};
		9706CC812BC3CFA90080FECE /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 9706CC5E2BC3CFA40080FECE /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 9706CC652BC3CFA40080FECE;
			remoteInfo = JunShi;
		};
/* End PBXContainerItemProxy section */

/* Begin PBXFileReference section */
		588685CA2E162D0800A4C3FF /* ArchivedChatsListView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ArchivedChatsListView.swift; sourceTree = "<group>"; };
		588685CB2E162D0800A4C3FF /* ArchivedChatView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ArchivedChatView.swift; sourceTree = "<group>"; };
		588685CC2E162D0800A4C3FF /* ArchivedChatViewModel.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ArchivedChatViewModel.swift; sourceTree = "<group>"; };
		588685CE2E162D0800A4C3FF /* SplashCodeSyntaxHighlighter.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = SplashCodeSyntaxHighlighter.swift; sourceTree = "<group>"; };
		588685CF2E162D0800A4C3FF /* TextOutputFormat.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = TextOutputFormat.swift; sourceTree = "<group>"; };
		588685D12E162D0800A4C3FF /* RecognizeFullScreenView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = RecognizeFullScreenView.swift; sourceTree = "<group>"; };
		588685D22E162D0800A4C3FF /* RecognizeMessageView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = RecognizeMessageView.swift; sourceTree = "<group>"; };
		588685D32E162D0800A4C3FF /* RecognizeSmallView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = RecognizeSmallView.swift; sourceTree = "<group>"; };
		588685D42E162D0800A4C3FF /* RecognizeView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = RecognizeView.swift; sourceTree = "<group>"; };
		588685D62E162D0800A4C3FF /* ChatsListView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ChatsListView.swift; sourceTree = "<group>"; };
		588685D72E162D0800A4C3FF /* ChatView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ChatView.swift; sourceTree = "<group>"; };
		588685D82E162D0800A4C3FF /* ChatViewPreview.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ChatViewPreview.swift; sourceTree = "<group>"; };
		588685D92E162D0800A4C3FF /* MessageBubble.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = MessageBubble.swift; sourceTree = "<group>"; };
		588685DA2E162D0800A4C3FF /* OptimizedMessageView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = OptimizedMessageView.swift; sourceTree = "<group>"; };
		588685DB2E162D0800A4C3FF /* ScrollPositionReader.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ScrollPositionReader.swift; sourceTree = "<group>"; };
		588685DC2E162D0800A4C3FF /* ScrollStateManager.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ScrollStateManager.swift; sourceTree = "<group>"; };
		588685DD2E162D0800A4C3FF /* SideMenuModifier.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = SideMenuModifier.swift; sourceTree = "<group>"; };
		588685DE2E162D0800A4C3FF /* TextSelectionView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = TextSelectionView.swift; sourceTree = "<group>"; };
		588685DF2E162D0800A4C3FF /* TypingIndicator.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = TypingIndicator.swift; sourceTree = "<group>"; };
		588685E12E162D0800A4C3FF /* EmptySessionView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = EmptySessionView.swift; sourceTree = "<group>"; };
		588685E22E162D0800A4C3FF /* EnhancedLoadingView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = EnhancedLoadingView.swift; sourceTree = "<group>"; };
		588685E42E162D0800A4C3FF /* InputBottomView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = InputBottomView.swift; sourceTree = "<group>"; };
		588685E52E162D0800A4C3FF /* InputField.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = InputField.swift; sourceTree = "<group>"; };
		588685E62E162D0800A4C3FF /* InputViewModel.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = InputViewModel.swift; sourceTree = "<group>"; };
		588685E82E162D0800A4C3FF /* EmailLoginView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = EmailLoginView.swift; sourceTree = "<group>"; };
		588685E92E162D0800A4C3FF /* InputingAnimationView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = InputingAnimationView.swift; sourceTree = "<group>"; };
		588685EA2E162D0800A4C3FF /* LoginView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = LoginView.swift; sourceTree = "<group>"; };
		588685EB2E162D0800A4C3FF /* PinCodeInputView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = PinCodeInputView.swift; sourceTree = "<group>"; };
		588685EC2E162D0800A4C3FF /* RegisterView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = RegisterView.swift; sourceTree = "<group>"; };
		588685ED2E162D0800A4C3FF /* VerificationView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = VerificationView.swift; sourceTree = "<group>"; };
		588685EF2E162D0800A4C3FF /* ContentView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ContentView.swift; sourceTree = "<group>"; };
		588685F02E162D0800A4C3FF /* SideMenu.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = SideMenu.swift; sourceTree = "<group>"; };
		588685F22E162D0800A4C3FF /* AboutView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = AboutView.swift; sourceTree = "<group>"; };
		588685F32E162D0800A4C3FF /* AppListView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = AppListView.swift; sourceTree = "<group>"; };
		588685F42E162D0800A4C3FF /* PreferLanguageView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = PreferLanguageView.swift; sourceTree = "<group>"; };
		588685F52E162D0800A4C3FF /* PricingView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = PricingView.swift; sourceTree = "<group>"; };
		588685F62E162D0800A4C3FF /* PurchaseView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = PurchaseView.swift; sourceTree = "<group>"; };
		588685F72E162D0800A4C3FF /* RechargeView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = RechargeView.swift; sourceTree = "<group>"; };
		588685F82E162D0800A4C3FF /* SettingsView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = SettingsView.swift; sourceTree = "<group>"; };
		588685F92E162D0800A4C3FF /* WebView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = WebView.swift; sourceTree = "<group>"; };
		588685FB2E162D0800A4C3FF /* SplashView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = SplashView.swift; sourceTree = "<group>"; };
		588685FD2E162D0800A4C3FF /* ChatConfigDatabaseManager.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ChatConfigDatabaseManager.swift; sourceTree = "<group>"; };
		588685FE2E162D0800A4C3FF /* MultiStepFormPreviewView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = MultiStepFormPreviewView.swift; sourceTree = "<group>"; };
		588685FF2E162D0800A4C3FF /* MultiStepFormView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = MultiStepFormView.swift; sourceTree = "<group>"; };
		588686002E162D0800A4C3FF /* MultiStepFormViewModel.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = MultiStepFormViewModel.swift; sourceTree = "<group>"; };
		588686012E162D0800A4C3FF /* PredefinedOptionKey.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = PredefinedOptionKey.swift; sourceTree = "<group>"; };
		588686022E162D0800A4C3FF /* StepPreviewView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = StepPreviewView.swift; sourceTree = "<group>"; };
		588686032E162D0800A4C3FF /* StepProtocol.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = StepProtocol.swift; sourceTree = "<group>"; };
		588686042E162D0800A4C3FF /* StepView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = StepView.swift; sourceTree = "<group>"; };
		588686052E162D0800A4C3FF /* StepViewModel.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = StepViewModel.swift; sourceTree = "<group>"; };
		588686072E162D0800A4C3FF /* MessageBubblePreview.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = MessageBubblePreview.swift; sourceTree = "<group>"; };
		588686082E162D0800A4C3FF /* UpdatePromptView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = UpdatePromptView.swift; sourceTree = "<group>"; };
		58DD65022E404CB1007F49EC /* AppStartupManager.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = AppStartupManager.swift; sourceTree = "<group>"; };
		58DD65052E404CBE007F49EC /* StartupLoadingView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = StartupLoadingView.swift; sourceTree = "<group>"; };
		58DD65122E405271007F49EC /* OptimizedChatRepository.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = OptimizedChatRepository.swift; sourceTree = "<group>"; };
		58DD65132E405271007F49EC /* OptimizedMessageRepository.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = OptimizedMessageRepository.swift; sourceTree = "<group>"; };
		58DD65242E405289007F49EC /* OptimizedChatRowView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = OptimizedChatRowView.swift; sourceTree = "<group>"; };
		58DD65282E405789007F49EC /* MessageRepositoryProtocol.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = MessageRepositoryProtocol.swift; sourceTree = "<group>"; };
		58DD652B2E405E76007F49EC /* APIRequestManager.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = APIRequestManager.swift; sourceTree = "<group>"; };
		58DD65322E4097C4007F49EC /* StepEditSection.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = StepEditSection.swift; sourceTree = "<group>"; };
		58DD65332E4097C4007F49EC /* UnifiedStepFormView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = UnifiedStepFormView.swift; sourceTree = "<group>"; };
		58EDE1AF2E38A40F00AB1BA8 /* CacheManager.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = CacheManager.swift; sourceTree = "<group>"; };
		58EDE1B12E38A40F00AB1BA8 /* BaseMigration.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = BaseMigration.swift; sourceTree = "<group>"; };
		58EDE1B32E38A40F00AB1BA8 /* ChatListRepository.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ChatListRepository.swift; sourceTree = "<group>"; };
		58EDE1B42E38A40F00AB1BA8 /* ChatRepository.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ChatRepository.swift; sourceTree = "<group>"; };
		58EDE1B52E38A40F00AB1BA8 /* ChatRepositoryProtocol.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ChatRepositoryProtocol.swift; sourceTree = "<group>"; };
		58EDE1B62E38A40F00AB1BA8 /* MessageRepository.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = MessageRepository.swift; sourceTree = "<group>"; };
		58EDE1B82E38A40F00AB1BA8 /* AdvisorDatabaseManager.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = AdvisorDatabaseManager.swift; sourceTree = "<group>"; };
		58EDE1B92E38A40F00AB1BA8 /* DatabaseIntegrityChecker.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = DatabaseIntegrityChecker.swift; sourceTree = "<group>"; };
		58EDE1BA2E38A40F00AB1BA8 /* DatabaseVersion.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = DatabaseVersion.swift; sourceTree = "<group>"; };
		58EDE1BB2E38A40F00AB1BA8 /* DataChangeNotificationCenter.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = DataChangeNotificationCenter.swift; sourceTree = "<group>"; };
		58EDE1BE2E38A40F00AB1BA8 /* DataFlowDebugger.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = DataFlowDebugger.swift; sourceTree = "<group>"; };
		58EDE1C02E38A40F00AB1BA8 /* ChatListComponentFactory.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ChatListComponentFactory.swift; sourceTree = "<group>"; };
		58EDE1C22E38A40F00AB1BA8 /* APICommom.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = APICommom.swift; sourceTree = "<group>"; };
		58EDE1C32E38A40F00AB1BA8 /* AuthTarget.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = AuthTarget.swift; sourceTree = "<group>"; };
		58EDE1C42E38A40F00AB1BA8 /* ChatTarget.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ChatTarget.swift; sourceTree = "<group>"; };
		58EDE1C52E38A40F00AB1BA8 /* ConfigTarget.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ConfigTarget.swift; sourceTree = "<group>"; };
		58EDE1C62E38A40F00AB1BA8 /* ConnectionManager.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ConnectionManager.swift; sourceTree = "<group>"; };
		58EDE1C72E38A40F00AB1BA8 /* ErrorRecoveryManager.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ErrorRecoveryManager.swift; sourceTree = "<group>"; };
		58EDE1C82E38A40F00AB1BA8 /* NetworkService.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = NetworkService.swift; sourceTree = "<group>"; };
		58EDE1C92E38A40F00AB1BA8 /* NetworkStatusManager.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = NetworkStatusManager.swift; sourceTree = "<group>"; };
		58EDE1CA2E38A40F00AB1BA8 /* PricingTarget.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = PricingTarget.swift; sourceTree = "<group>"; };
		58EDE1CB2E38A40F00AB1BA8 /* PurchaseTarget.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = PurchaseTarget.swift; sourceTree = "<group>"; };
		58EDE1CC2E38A40F00AB1BA8 /* UnifiedSSEManager.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = UnifiedSSEManager.swift; sourceTree = "<group>"; };
		58EDE1D02E38A40F00AB1BA8 /* AccountManager.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = AccountManager.swift; sourceTree = "<group>"; };
		58EDE1D12E38A40F00AB1BA8 /* BootsManager.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = BootsManager.swift; sourceTree = "<group>"; };
		58EDE1D22E38A40F00AB1BA8 /* ChatSessionManager.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ChatSessionManager.swift; sourceTree = "<group>"; };
		58EDE1D32E38A40F00AB1BA8 /* MessageLoader.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = MessageLoader.swift; sourceTree = "<group>"; };
		58EDE1D42E38A40F00AB1BA8 /* Preferences.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = Preferences.swift; sourceTree = "<group>"; };
		58EDE1D52E38A40F00AB1BA8 /* ShopManager.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ShopManager.swift; sourceTree = "<group>"; };
		58EDE1D62E38A40F00AB1BA8 /* TypewriterEffectManager.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = TypewriterEffectManager.swift; sourceTree = "<group>"; };
		58EDE1D72E38A40F00AB1BA8 /* UserSettings.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = UserSettings.swift; sourceTree = "<group>"; };
		58EDE1D82E38A40F00AB1BA8 /* VersionUpdateManager.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = VersionUpdateManager.swift; sourceTree = "<group>"; };
		58EDE21E2E38A42C00AB1BA8 /* ChatUIStateManager.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ChatUIStateManager.swift; sourceTree = "<group>"; };
		58EDE21F2E38A42C00AB1BA8 /* MessageManager.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = MessageManager.swift; sourceTree = "<group>"; };
		58EDE2212E38A42C00AB1BA8 /* ChatListCoordinator.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ChatListCoordinator.swift; sourceTree = "<group>"; };
		58EDE2222E38A42C00AB1BA8 /* ChatListDataSource.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ChatListDataSource.swift; sourceTree = "<group>"; };
		58EDE2232E38A42C00AB1BA8 /* ChatListUIStateManager.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ChatListUIStateManager.swift; sourceTree = "<group>"; };
		58EDE2242E38A42C00AB1BA8 /* RefactoredChatListViewModel.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = RefactoredChatListViewModel.swift; sourceTree = "<group>"; };
		58EDE2262E38A42C00AB1BA8 /* AuthViewModel.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = AuthViewModel.swift; sourceTree = "<group>"; };
		58EDE2272E38A42C00AB1BA8 /* ChatListViewModel.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ChatListViewModel.swift; sourceTree = "<group>"; };
		58EDE2282E38A42C00AB1BA8 /* ChatViewModel.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ChatViewModel.swift; sourceTree = "<group>"; };
		58EDE2292E38A42C00AB1BA8 /* ContentViewModel.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ContentViewModel.swift; sourceTree = "<group>"; };
		58EDE22A2E38A42C00AB1BA8 /* PricingViewModel.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = PricingViewModel.swift; sourceTree = "<group>"; };
		58EDE22B2E38A42C00AB1BA8 /* PurchaseViewModel.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = PurchaseViewModel.swift; sourceTree = "<group>"; };
		58EDE22C2E38A42C00AB1BA8 /* RecognizeViewModel.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = RecognizeViewModel.swift; sourceTree = "<group>"; };
		58EDE22D2E38A42C00AB1BA8 /* RegisterViewModel.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = RegisterViewModel.swift; sourceTree = "<group>"; };
		58EDE22E2E38A42C00AB1BA8 /* SplashViewModel.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = SplashViewModel.swift; sourceTree = "<group>"; };
		58EDE22F2E38A42C00AB1BA8 /* VerificationViewModel.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = VerificationViewModel.swift; sourceTree = "<group>"; };
		6C0310782CDC6DDF00E0C305 /* AppDelegate.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = AppDelegate.swift; sourceTree = "<group>"; };
		6C03107E2CDC9AEF00E0C305 /* fil */ = {isa = PBXFileReference; lastKnownFileType = text.plist.strings; name = fil; path = fil.lproj/InfoPlist.strings; sourceTree = "<group>"; };
		6C0310802CDC9B1300E0C305 /* fil */ = {isa = PBXFileReference; lastKnownFileType = text.plist.strings; name = fil; path = fil.lproj/Localizable.strings; sourceTree = "<group>"; };
		6C0CBB0E2BF756DB009036A5 /* WeakList.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = WeakList.swift; sourceTree = "<group>"; };
		6C17C4DB2C083970005BA6A3 /* VibrationController.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = VibrationController.swift; sourceTree = "<group>"; };
		6C17C4E02C09715B005BA6A3 /* Base */ = {isa = PBXFileReference; lastKnownFileType = text.plist.xml; name = Base; path = Base.lproj/Info.plist; sourceTree = "<group>"; };
		6C17C4E72C09BDAD005BA6A3 /* Product.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = Product.swift; sourceTree = "<group>"; };
		6C18B8372CC5EF660029CB87 /* AdSupport.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = AdSupport.framework; path = System/Library/Frameworks/AdSupport.framework; sourceTree = SDKROOT; };
		6C1FCBAD2C5724E400706697 /* URLExtension.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = URLExtension.swift; sourceTree = "<group>"; };
		6C3000D42BDF70DE0079AF38 /* OpenAIModel.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = OpenAIModel.swift; sourceTree = "<group>"; };
		6C5F8B5B2CE1DE1300B2E3F9 /* fa */ = {isa = PBXFileReference; lastKnownFileType = text.plist.strings; name = fa; path = fa.lproj/Localizable.strings; sourceTree = "<group>"; };
		6C5F8B5C2CE1DE1300B2E3F9 /* fa */ = {isa = PBXFileReference; lastKnownFileType = text.plist.strings; name = fa; path = fa.lproj/InfoPlist.strings; sourceTree = "<group>"; };
		6C5F8B5E2CE1DF1F00B2E3F9 /* ur-PK */ = {isa = PBXFileReference; lastKnownFileType = text.plist.strings; name = "ur-PK"; path = "ur-PK.lproj/Localizable.strings"; sourceTree = "<group>"; };
		6C5F8B5F2CE1DF1F00B2E3F9 /* ur-PK */ = {isa = PBXFileReference; lastKnownFileType = text.plist.strings; name = "ur-PK"; path = "ur-PK.lproj/InfoPlist.strings"; sourceTree = "<group>"; };
		6C5F8B612CE1DFEA00B2E3F9 /* ps */ = {isa = PBXFileReference; lastKnownFileType = text.plist.strings; name = ps; path = ps.lproj/Localizable.strings; sourceTree = "<group>"; };
		6C5F8B622CE1DFEA00B2E3F9 /* ps */ = {isa = PBXFileReference; lastKnownFileType = text.plist.strings; name = ps; path = ps.lproj/InfoPlist.strings; sourceTree = "<group>"; };
		6C5F8B642CE1E0EE00B2E3F9 /* fr-CI */ = {isa = PBXFileReference; lastKnownFileType = text.plist.strings; name = "fr-CI"; path = "fr-CI.lproj/Localizable.strings"; sourceTree = "<group>"; };
		6C5F8B652CE1E0EE00B2E3F9 /* fr-CI */ = {isa = PBXFileReference; lastKnownFileType = text.plist.strings; name = "fr-CI"; path = "fr-CI.lproj/InfoPlist.strings"; sourceTree = "<group>"; };
		6C62A6AF2C461FDE002D40D5 /* en-AU */ = {isa = PBXFileReference; lastKnownFileType = text.plist.strings; name = "en-AU"; path = "en-AU.lproj/Localizable.strings"; sourceTree = "<group>"; };
		6C62A6B02C461FDE002D40D5 /* en-AU */ = {isa = PBXFileReference; lastKnownFileType = text.plist.strings; name = "en-AU"; path = "en-AU.lproj/InfoPlist.strings"; sourceTree = "<group>"; };
		6C62A6B22C462038002D40D5 /* en-IN */ = {isa = PBXFileReference; lastKnownFileType = text.plist.strings; name = "en-IN"; path = "en-IN.lproj/Localizable.strings"; sourceTree = "<group>"; };
		6C62A6B32C462038002D40D5 /* en-IN */ = {isa = PBXFileReference; lastKnownFileType = text.plist.strings; name = "en-IN"; path = "en-IN.lproj/InfoPlist.strings"; sourceTree = "<group>"; };
		6C62A6B52C46204B002D40D5 /* en-GB */ = {isa = PBXFileReference; lastKnownFileType = text.plist.strings; name = "en-GB"; path = "en-GB.lproj/Localizable.strings"; sourceTree = "<group>"; };
		6C62A6B62C46204C002D40D5 /* en-GB */ = {isa = PBXFileReference; lastKnownFileType = text.plist.strings; name = "en-GB"; path = "en-GB.lproj/InfoPlist.strings"; sourceTree = "<group>"; };
		6C62A6B82C462051002D40D5 /* fr-CA */ = {isa = PBXFileReference; lastKnownFileType = text.plist.strings; name = "fr-CA"; path = "fr-CA.lproj/Localizable.strings"; sourceTree = "<group>"; };
		6C62A6B92C462051002D40D5 /* fr-CA */ = {isa = PBXFileReference; lastKnownFileType = text.plist.strings; name = "fr-CA"; path = "fr-CA.lproj/InfoPlist.strings"; sourceTree = "<group>"; };
		6C62A6BB2C462055002D40D5 /* pt-PT */ = {isa = PBXFileReference; lastKnownFileType = text.plist.strings; name = "pt-PT"; path = "pt-PT.lproj/Localizable.strings"; sourceTree = "<group>"; };
		6C62A6BC2C462055002D40D5 /* pt-PT */ = {isa = PBXFileReference; lastKnownFileType = text.plist.strings; name = "pt-PT"; path = "pt-PT.lproj/InfoPlist.strings"; sourceTree = "<group>"; };
		6C62A6C12C465639002D40D5 /* AppReviewManager.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = AppReviewManager.swift; sourceTree = "<group>"; };
		6C6690BB2C2EDA6B00FA97E1 /* ColorExtension.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ColorExtension.swift; sourceTree = "<group>"; };
		6C6690BD2C2EDA9500FA97E1 /* UserDefaultsExtension.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = UserDefaultsExtension.swift; sourceTree = "<group>"; };
		6C6690BF2C2EE46000FA97E1 /* FirebaseManager.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = FirebaseManager.swift; sourceTree = "<group>"; };
		6C6E215D2C364B1D0081A7D6 /* RecognizedText.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = RecognizedText.swift; sourceTree = "<group>"; };
		6C6E228C2C3783930081A7D6 /* hr */ = {isa = PBXFileReference; lastKnownFileType = text.plist.strings; name = hr; path = hr.lproj/Localizable.strings; sourceTree = "<group>"; };
		6C6E228D2C3783930081A7D6 /* hr */ = {isa = PBXFileReference; lastKnownFileType = text.plist.strings; name = hr; path = hr.lproj/InfoPlist.strings; sourceTree = "<group>"; };
		6C6E228F2C37839D0081A7D6 /* cs */ = {isa = PBXFileReference; lastKnownFileType = text.plist.strings; name = cs; path = cs.lproj/Localizable.strings; sourceTree = "<group>"; };
		6C6E22902C37839D0081A7D6 /* cs */ = {isa = PBXFileReference; lastKnownFileType = text.plist.strings; name = cs; path = cs.lproj/InfoPlist.strings; sourceTree = "<group>"; };
		6C6E22922C3783A00081A7D6 /* da */ = {isa = PBXFileReference; lastKnownFileType = text.plist.strings; name = da; path = da.lproj/Localizable.strings; sourceTree = "<group>"; };
		6C6E22932C3783A00081A7D6 /* da */ = {isa = PBXFileReference; lastKnownFileType = text.plist.strings; name = da; path = da.lproj/InfoPlist.strings; sourceTree = "<group>"; };
		6C6E22952C3783A50081A7D6 /* nl */ = {isa = PBXFileReference; lastKnownFileType = text.plist.strings; name = nl; path = nl.lproj/Localizable.strings; sourceTree = "<group>"; };
		6C6E22962C3783A50081A7D6 /* nl */ = {isa = PBXFileReference; lastKnownFileType = text.plist.strings; name = nl; path = nl.lproj/InfoPlist.strings; sourceTree = "<group>"; };
		6C6E22982C3783AB0081A7D6 /* fi */ = {isa = PBXFileReference; lastKnownFileType = text.plist.strings; name = fi; path = fi.lproj/Localizable.strings; sourceTree = "<group>"; };
		6C6E22992C3783AB0081A7D6 /* fi */ = {isa = PBXFileReference; lastKnownFileType = text.plist.strings; name = fi; path = fi.lproj/InfoPlist.strings; sourceTree = "<group>"; };
		6C6E229B2C3783B00081A7D6 /* el */ = {isa = PBXFileReference; lastKnownFileType = text.plist.strings; name = el; path = el.lproj/Localizable.strings; sourceTree = "<group>"; };
		6C6E229C2C3783B10081A7D6 /* el */ = {isa = PBXFileReference; lastKnownFileType = text.plist.strings; name = el; path = el.lproj/InfoPlist.strings; sourceTree = "<group>"; };
		6C6E229E2C3783B40081A7D6 /* he */ = {isa = PBXFileReference; lastKnownFileType = text.plist.strings; name = he; path = he.lproj/Localizable.strings; sourceTree = "<group>"; };
		6C6E229F2C3783B40081A7D6 /* he */ = {isa = PBXFileReference; lastKnownFileType = text.plist.strings; name = he; path = he.lproj/InfoPlist.strings; sourceTree = "<group>"; };
		6C6E22A12C3783B70081A7D6 /* hi */ = {isa = PBXFileReference; lastKnownFileType = text.plist.strings; name = hi; path = hi.lproj/Localizable.strings; sourceTree = "<group>"; };
		6C6E22A22C3783B70081A7D6 /* hi */ = {isa = PBXFileReference; lastKnownFileType = text.plist.strings; name = hi; path = hi.lproj/InfoPlist.strings; sourceTree = "<group>"; };
		6C6E22A42C3783CE0081A7D6 /* hu */ = {isa = PBXFileReference; lastKnownFileType = text.plist.strings; name = hu; path = hu.lproj/Localizable.strings; sourceTree = "<group>"; };
		6C6E22A52C3783CE0081A7D6 /* hu */ = {isa = PBXFileReference; lastKnownFileType = text.plist.strings; name = hu; path = hu.lproj/InfoPlist.strings; sourceTree = "<group>"; };
		6C6E22A72C3783D30081A7D6 /* id */ = {isa = PBXFileReference; lastKnownFileType = text.plist.strings; name = id; path = id.lproj/Localizable.strings; sourceTree = "<group>"; };
		6C6E22A82C3783D30081A7D6 /* id */ = {isa = PBXFileReference; lastKnownFileType = text.plist.strings; name = id; path = id.lproj/InfoPlist.strings; sourceTree = "<group>"; };
		6C6E22AA2C3783D90081A7D6 /* nb */ = {isa = PBXFileReference; lastKnownFileType = text.plist.strings; name = nb; path = nb.lproj/Localizable.strings; sourceTree = "<group>"; };
		6C6E22AB2C3783D90081A7D6 /* nb */ = {isa = PBXFileReference; lastKnownFileType = text.plist.strings; name = nb; path = nb.lproj/InfoPlist.strings; sourceTree = "<group>"; };
		6C6E22AD2C3783DE0081A7D6 /* ro */ = {isa = PBXFileReference; lastKnownFileType = text.plist.strings; name = ro; path = ro.lproj/Localizable.strings; sourceTree = "<group>"; };
		6C6E22AE2C3783DE0081A7D6 /* ro */ = {isa = PBXFileReference; lastKnownFileType = text.plist.strings; name = ro; path = ro.lproj/InfoPlist.strings; sourceTree = "<group>"; };
		6C6E22B02C3783E20081A7D6 /* sk */ = {isa = PBXFileReference; lastKnownFileType = text.plist.strings; name = sk; path = sk.lproj/Localizable.strings; sourceTree = "<group>"; };
		6C6E22B12C3783E20081A7D6 /* sk */ = {isa = PBXFileReference; lastKnownFileType = text.plist.strings; name = sk; path = sk.lproj/InfoPlist.strings; sourceTree = "<group>"; };
		6C6E22B32C3783E80081A7D6 /* sv */ = {isa = PBXFileReference; lastKnownFileType = text.plist.strings; name = sv; path = sv.lproj/Localizable.strings; sourceTree = "<group>"; };
		6C6E22B42C3783E80081A7D6 /* sv */ = {isa = PBXFileReference; lastKnownFileType = text.plist.strings; name = sv; path = sv.lproj/InfoPlist.strings; sourceTree = "<group>"; };
		6C6E22B62C3783F70081A7D6 /* ab */ = {isa = PBXFileReference; lastKnownFileType = text.plist.strings; name = ab; path = ab.lproj/Localizable.strings; sourceTree = "<group>"; };
		6C6E22B72C3783F70081A7D6 /* ab */ = {isa = PBXFileReference; lastKnownFileType = text.plist.strings; name = ab; path = ab.lproj/InfoPlist.strings; sourceTree = "<group>"; };
		6C9739622BDE2E5900F53F1D /* AppThemes.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = AppThemes.swift; sourceTree = "<group>"; };
		6CA0CEA72BD24B8600A6B32B /* StringExtension.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = StringExtension.swift; sourceTree = "<group>"; };
		6CA0CEAC2BD251EB00A6B32B /* ViewExtension.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ViewExtension.swift; sourceTree = "<group>"; };
		6CA36DE12C1833EB00AE82B2 /* en */ = {isa = PBXFileReference; lastKnownFileType = text.plist.strings; name = en; path = en.lproj/InfoPlist.strings; sourceTree = "<group>"; };
		6CA36DE32C1833EF00AE82B2 /* ar */ = {isa = PBXFileReference; lastKnownFileType = text.plist.strings; name = ar; path = ar.lproj/InfoPlist.strings; sourceTree = "<group>"; };
		6CA36DE42C1833F100AE82B2 /* zh-Hans */ = {isa = PBXFileReference; lastKnownFileType = text.plist.strings; name = "zh-Hans"; path = "zh-Hans.lproj/InfoPlist.strings"; sourceTree = "<group>"; };
		6CA36DE52C1833F200AE82B2 /* ja */ = {isa = PBXFileReference; lastKnownFileType = text.plist.strings; name = ja; path = ja.lproj/InfoPlist.strings; sourceTree = "<group>"; };
		6CA36DE82C1833F500AE82B2 /* ru */ = {isa = PBXFileReference; lastKnownFileType = text.plist.strings; name = ru; path = ru.lproj/InfoPlist.strings; sourceTree = "<group>"; };
		6CA36DE92C1833F600AE82B2 /* th */ = {isa = PBXFileReference; lastKnownFileType = text.plist.strings; name = th; path = th.lproj/InfoPlist.strings; sourceTree = "<group>"; };
		6CA36DEA2C1833F600AE82B2 /* tr */ = {isa = PBXFileReference; lastKnownFileType = text.plist.strings; name = tr; path = tr.lproj/InfoPlist.strings; sourceTree = "<group>"; };
		6CA36DEB2C1833F700AE82B2 /* uk */ = {isa = PBXFileReference; lastKnownFileType = text.plist.strings; name = uk; path = uk.lproj/InfoPlist.strings; sourceTree = "<group>"; };
		6CA36DEC2C1833F700AE82B2 /* vi */ = {isa = PBXFileReference; lastKnownFileType = text.plist.strings; name = vi; path = vi.lproj/InfoPlist.strings; sourceTree = "<group>"; };
		6CA36E172C1D337F00AE82B2 /* ga */ = {isa = PBXFileReference; lastKnownFileType = text.plist.strings; name = ga; path = ga.lproj/Localizable.strings; sourceTree = "<group>"; };
		6CA36E182C1D337F00AE82B2 /* ga */ = {isa = PBXFileReference; lastKnownFileType = text.plist.strings; name = ga; path = ga.lproj/InfoPlist.strings; sourceTree = "<group>"; };
		6CA36E1A2C1D33A000AE82B2 /* zh-HK */ = {isa = PBXFileReference; lastKnownFileType = text.plist.strings; name = "zh-HK"; path = "zh-HK.lproj/Localizable.strings"; sourceTree = "<group>"; };
		6CA36E1B2C1D33A000AE82B2 /* zh-HK */ = {isa = PBXFileReference; lastKnownFileType = text.plist.strings; name = "zh-HK"; path = "zh-HK.lproj/InfoPlist.strings"; sourceTree = "<group>"; };
		6CA36E1D2C1D33BC00AE82B2 /* zh-Hant */ = {isa = PBXFileReference; lastKnownFileType = text.plist.strings; name = "zh-Hant"; path = "zh-Hant.lproj/Localizable.strings"; sourceTree = "<group>"; };
		6CA36E1E2C1D33BC00AE82B2 /* zh-Hant */ = {isa = PBXFileReference; lastKnownFileType = text.plist.strings; name = "zh-Hant"; path = "zh-Hant.lproj/InfoPlist.strings"; sourceTree = "<group>"; };
		6CA36E202C1D341900AE82B2 /* lo */ = {isa = PBXFileReference; lastKnownFileType = text.plist.strings; name = lo; path = lo.lproj/Localizable.strings; sourceTree = "<group>"; };
		6CA36E212C1D341900AE82B2 /* lo */ = {isa = PBXFileReference; lastKnownFileType = text.plist.strings; name = lo; path = lo.lproj/InfoPlist.strings; sourceTree = "<group>"; };
		6CA36E232C1D359800AE82B2 /* ms */ = {isa = PBXFileReference; lastKnownFileType = text.plist.strings; name = ms; path = ms.lproj/Localizable.strings; sourceTree = "<group>"; };
		6CA36E242C1D359800AE82B2 /* ms */ = {isa = PBXFileReference; lastKnownFileType = text.plist.strings; name = ms; path = ms.lproj/InfoPlist.strings; sourceTree = "<group>"; };
		6CA36E262C1D362100AE82B2 /* es */ = {isa = PBXFileReference; lastKnownFileType = text.plist.strings; name = es; path = es.lproj/Localizable.strings; sourceTree = "<group>"; };
		6CA36E272C1D362100AE82B2 /* es */ = {isa = PBXFileReference; lastKnownFileType = text.plist.strings; name = es; path = es.lproj/InfoPlist.strings; sourceTree = "<group>"; };
		6CA36E292C1D366000AE82B2 /* de */ = {isa = PBXFileReference; lastKnownFileType = text.plist.strings; name = de; path = de.lproj/Localizable.strings; sourceTree = "<group>"; };
		6CA36E2A2C1D366000AE82B2 /* de */ = {isa = PBXFileReference; lastKnownFileType = text.plist.strings; name = de; path = de.lproj/InfoPlist.strings; sourceTree = "<group>"; };
		6CA36E2C2C1D36A300AE82B2 /* ca */ = {isa = PBXFileReference; lastKnownFileType = text.plist.strings; name = ca; path = ca.lproj/Localizable.strings; sourceTree = "<group>"; };
		6CA36E2D2C1D36A300AE82B2 /* ca */ = {isa = PBXFileReference; lastKnownFileType = text.plist.strings; name = ca; path = ca.lproj/InfoPlist.strings; sourceTree = "<group>"; };
		6CA36E2F2C1D36ED00AE82B2 /* fr */ = {isa = PBXFileReference; lastKnownFileType = text.plist.strings; name = fr; path = fr.lproj/Localizable.strings; sourceTree = "<group>"; };
		6CA36E302C1D36ED00AE82B2 /* fr */ = {isa = PBXFileReference; lastKnownFileType = text.plist.strings; name = fr; path = fr.lproj/InfoPlist.strings; sourceTree = "<group>"; };
		6CA36E322C1D372500AE82B2 /* it */ = {isa = PBXFileReference; lastKnownFileType = text.plist.strings; name = it; path = it.lproj/Localizable.strings; sourceTree = "<group>"; };
		6CA36E332C1D372500AE82B2 /* it */ = {isa = PBXFileReference; lastKnownFileType = text.plist.strings; name = it; path = it.lproj/InfoPlist.strings; sourceTree = "<group>"; };
		6CA36E352C1D38FC00AE82B2 /* ko */ = {isa = PBXFileReference; lastKnownFileType = text.plist.strings; name = ko; path = ko.lproj/Localizable.strings; sourceTree = "<group>"; };
		6CA36E362C1D38FC00AE82B2 /* ko */ = {isa = PBXFileReference; lastKnownFileType = text.plist.strings; name = ko; path = ko.lproj/InfoPlist.strings; sourceTree = "<group>"; };
		6CA36E382C1D395300AE82B2 /* pl */ = {isa = PBXFileReference; lastKnownFileType = text.plist.strings; name = pl; path = pl.lproj/Localizable.strings; sourceTree = "<group>"; };
		6CA36E392C1D395300AE82B2 /* pl */ = {isa = PBXFileReference; lastKnownFileType = text.plist.strings; name = pl; path = pl.lproj/InfoPlist.strings; sourceTree = "<group>"; };
		6CA36E3B2C1D3A1300AE82B2 /* es-419 */ = {isa = PBXFileReference; lastKnownFileType = text.plist.strings; name = "es-419"; path = "es-419.lproj/Localizable.strings"; sourceTree = "<group>"; };
		6CA36E3C2C1D3A1300AE82B2 /* es-419 */ = {isa = PBXFileReference; lastKnownFileType = text.plist.strings; name = "es-419"; path = "es-419.lproj/InfoPlist.strings"; sourceTree = "<group>"; };
		6CA36E3E2C1D3A6000AE82B2 /* pt-BR */ = {isa = PBXFileReference; lastKnownFileType = text.plist.strings; name = "pt-BR"; path = "pt-BR.lproj/Localizable.strings"; sourceTree = "<group>"; };
		6CA36E3F2C1D3A6000AE82B2 /* pt-BR */ = {isa = PBXFileReference; lastKnownFileType = text.plist.strings; name = "pt-BR"; path = "pt-BR.lproj/InfoPlist.strings"; sourceTree = "<group>"; };
		6CE61D462BEC7D8B00B8B4F1 /* DateExtension .swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = "DateExtension .swift"; sourceTree = "<group>"; };
		6CE61D702BF3001600B8B4F1 /* ChatAdvisor.entitlements */ = {isa = PBXFileReference; lastKnownFileType = text.plist.entitlements; path = ChatAdvisor.entitlements; sourceTree = "<group>"; };
		6CE61D712BF3041900B8B4F1 /* PrivacyInfo.xcprivacy */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text.xml; path = PrivacyInfo.xcprivacy; sourceTree = "<group>"; };
		6CFCC22D2D65AB19004E0912 /* en */ = {isa = PBXFileReference; lastKnownFileType = text.plist.xml; name = en; path = en.lproj/Info.plist; sourceTree = "<group>"; };
		9706CC662BC3CFA40080FECE /* ChatAdvisor.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = ChatAdvisor.app; sourceTree = BUILT_PRODUCTS_DIR; };
		9706CC692BC3CFA40080FECE /* ChatAdvisorApp.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ChatAdvisorApp.swift; sourceTree = "<group>"; };
		9706CC6D2BC3CFA80080FECE /* Assets.xcassets */ = {isa = PBXFileReference; lastKnownFileType = folder.assetcatalog; path = Assets.xcassets; sourceTree = "<group>"; };
		9706CC702BC3CFA80080FECE /* Preview Assets.xcassets */ = {isa = PBXFileReference; lastKnownFileType = folder.assetcatalog; path = "Preview Assets.xcassets"; sourceTree = "<group>"; };
		9706CC762BC3CFA80080FECE /* ChatAdvisorTests.xctest */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; path = ChatAdvisorTests.xctest; sourceTree = BUILT_PRODUCTS_DIR; };
		9706CC7A2BC3CFA80080FECE /* JunShiTests.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = JunShiTests.swift; sourceTree = "<group>"; };
		9706CC802BC3CFA90080FECE /* ChatAdvisorUITests.xctest */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; path = ChatAdvisorUITests.xctest; sourceTree = BUILT_PRODUCTS_DIR; };
		9706CC842BC3CFA90080FECE /* JunShiUITests.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = JunShiUITests.swift; sourceTree = "<group>"; };
		9706CC862BC3CFA90080FECE /* JunShiUITestsLaunchTests.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = JunShiUITestsLaunchTests.swift; sourceTree = "<group>"; };
		9706CC9F2BC3ED460080FECE /* GoogleService-Info.plist */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text.plist.xml; path = "GoogleService-Info.plist"; sourceTree = "<group>"; };
		9706CCB72BC3F2730080FECE /* ChatAdvisor Debug.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = "ChatAdvisor Debug.app"; sourceTree = BUILT_PRODUCTS_DIR; };
		D61300652BF3B561006C86C0 /* ChatsModel.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = ChatsModel.swift; sourceTree = "<group>"; };
		D62099602C0B8731000178CF /* Configs.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = Configs.swift; sourceTree = "<group>"; };
		D64340262BF8C33B00F62E35 /* Balance.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = Balance.swift; sourceTree = "<group>"; };
		D643402B2BF8D22000F62E35 /* StoreKit.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = StoreKit.framework; path = System/Library/Frameworks/StoreKit.framework; sourceTree = SDKROOT; };
		D65D5CD82C232D14004504BE /* Bugly.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; path = Bugly.framework; sourceTree = "<group>"; };
		D65D5CDA2C232D2B004504BE /* SystemConfiguration.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = SystemConfiguration.framework; path = System/Library/Frameworks/SystemConfiguration.framework; sourceTree = SDKROOT; };
		D65D5CDC2C232D34004504BE /* Security.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = Security.framework; path = System/Library/Frameworks/Security.framework; sourceTree = SDKROOT; };
		D65D5CDE2C232D4A004504BE /* libz.tbd */ = {isa = PBXFileReference; lastKnownFileType = "sourcecode.text-based-dylib-definition"; name = libz.tbd; path = usr/lib/libz.tbd; sourceTree = SDKROOT; };
		D65D5CDF2C232D55004504BE /* libc++.tbd */ = {isa = PBXFileReference; lastKnownFileType = "sourcecode.text-based-dylib-definition"; name = "libc++.tbd"; path = "usr/lib/libc++.tbd"; sourceTree = SDKROOT; };
		D6C63C9D2BCD6A420004BF5E /* CryptoFunction.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = CryptoFunction.swift; sourceTree = "<group>"; };
		D6C63CAE2BCD6A420004BF5E /* Chats.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = Chats.swift; sourceTree = "<group>"; };
		D6C63CAF2BCD6A420004BF5E /* NetworkResponse.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = NetworkResponse.swift; sourceTree = "<group>"; };
		D6C63CB02BCD6A420004BF5E /* User.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = User.swift; sourceTree = "<group>"; };
		D6DC913E2C0C6C1F009DB0AB /* en */ = {isa = PBXFileReference; lastKnownFileType = text.plist.strings; name = en; path = en.lproj/Localizable.strings; sourceTree = "<group>"; };
		D6DC91402C0C6C28009DB0AB /* ar */ = {isa = PBXFileReference; lastKnownFileType = text.plist.strings; name = ar; path = ar.lproj/Localizable.strings; sourceTree = "<group>"; };
		D6DC91412C0C6C2B009DB0AB /* zh-Hans */ = {isa = PBXFileReference; lastKnownFileType = text.plist.strings; name = "zh-Hans"; path = "zh-Hans.lproj/Localizable.strings"; sourceTree = "<group>"; };
		D6DC91422C0C6C2C009DB0AB /* ja */ = {isa = PBXFileReference; lastKnownFileType = text.plist.strings; name = ja; path = ja.lproj/Localizable.strings; sourceTree = "<group>"; };
		D6DC91442C0C6C2F009DB0AB /* ru */ = {isa = PBXFileReference; lastKnownFileType = text.plist.strings; name = ru; path = ru.lproj/Localizable.strings; sourceTree = "<group>"; };
		D6DC91452C0C6C30009DB0AB /* th */ = {isa = PBXFileReference; lastKnownFileType = text.plist.strings; name = th; path = th.lproj/Localizable.strings; sourceTree = "<group>"; };
		D6DC91462C0C6C31009DB0AB /* tr */ = {isa = PBXFileReference; lastKnownFileType = text.plist.strings; name = tr; path = tr.lproj/Localizable.strings; sourceTree = "<group>"; };
		D6DC91472C0C6C32009DB0AB /* uk */ = {isa = PBXFileReference; lastKnownFileType = text.plist.strings; name = uk; path = uk.lproj/Localizable.strings; sourceTree = "<group>"; };
		D6DC91482C0C6C32009DB0AB /* vi */ = {isa = PBXFileReference; lastKnownFileType = text.plist.strings; name = vi; path = vi.lproj/Localizable.strings; sourceTree = "<group>"; };
		D6DC914A2C1205B3009DB0AB /* UIDeviceExtension.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = UIDeviceExtension.swift; sourceTree = "<group>"; };
/* End PBXFileReference section */

/* Begin PBXFrameworksBuildPhase section */
		9706CC632BC3CFA40080FECE /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				D65D5CD92C232D14004504BE /* Bugly.framework in Frameworks */,
				6CD675312BF5EB58008134B8 /* Splash in Frameworks */,
				D643402C2BF8D22000F62E35 /* StoreKit.framework in Frameworks */,
				6C3987382BEB26CF0026E28C /* LDSwiftEventSource in Frameworks */,
				6CA0CEAB2BD24C1C00A6B32B /* SwifterSwift in Frameworks */,
				D6C84FE62BC436D0005FBCD6 /* FirebaseCrashlytics in Frameworks */,
				D65D5CDB2C232D2B004504BE /* SystemConfiguration.framework in Frameworks */,
				6C62A6C52C48C7AC002D40D5 /* EventSource in Frameworks */,
				6CDA93F82BF483EA00C52DDF /* MarkdownUI in Frameworks */,
				6C6E214D2C33B2170081A7D6 /* FirebaseMessaging in Frameworks */,
				9777D1082BC7DB3200619386 /* Alamofire in Frameworks */,
				D66B674F2C2B03EB00DB7598 /* GoogleSignInSwift in Frameworks */,
				6CC844282BD2096A00B4698C /* KeychainSwift in Frameworks */,
				6CC844332BD21B8800B4698C /* Localize_Swift in Frameworks */,
				9706CC9E2BC3ECB50080FECE /* FirebaseAnalytics in Frameworks */,
				6C90D6962CE763A600AD016E /* TikTokOpenSDKCore in Frameworks */,
				6C4529D32C52262100489F07 /* FacebookLogin in Frameworks */,
				6C4529D12C52262100489F07 /* FacebookCore in Frameworks */,
				9777D1052BC7DAEE00619386 /* Moya in Frameworks */,
				6CFEB0002BE0DFD70046C53A /* WCDBSwift in Frameworks */,
				6CE61D682BEF02D400B8B4F1 /* SimpleToast in Frameworks */,
				6C90D6982CE7645300AD016E /* TikTokOpenAuthSDK in Frameworks */,
				6C18B8382CC5EF660029CB87 /* AdSupport.framework in Frameworks */,
				6C71C88D2C3CCD020001B917 /* StepperView in Frameworks */,
				D65D5CDD2C232D34004504BE /* Security.framework in Frameworks */,
				6C9739612BDE2DFB00F53F1D /* LookinServer in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		9706CC732BC3CFA80080FECE /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		9706CC7D2BC3CFA90080FECE /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		9706CCAB2BC3F2730080FECE /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				9706CCAC2BC3F2730080FECE /* JunShiPackage in Frameworks */,
				9706CCAD2BC3F2730080FECE /* JunShiPackage in Frameworks */,
				9706CCAE2BC3F2730080FECE /* FirebaseAnalytics in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		588685CD2E162D0800A4C3FF /* Archived */ = {
			isa = PBXGroup;
			children = (
				588685CA2E162D0800A4C3FF /* ArchivedChatsListView.swift */,
				588685CB2E162D0800A4C3FF /* ArchivedChatView.swift */,
				588685CC2E162D0800A4C3FF /* ArchivedChatViewModel.swift */,
			);
			path = Archived;
			sourceTree = "<group>";
		};
		588685D02E162D0800A4C3FF /* MarkDown */ = {
			isa = PBXGroup;
			children = (
				588685CE2E162D0800A4C3FF /* SplashCodeSyntaxHighlighter.swift */,
				588685CF2E162D0800A4C3FF /* TextOutputFormat.swift */,
			);
			path = MarkDown;
			sourceTree = "<group>";
		};
		588685D52E162D0800A4C3FF /* Recognize */ = {
			isa = PBXGroup;
			children = (
				588685D12E162D0800A4C3FF /* RecognizeFullScreenView.swift */,
				588685D22E162D0800A4C3FF /* RecognizeMessageView.swift */,
				588685D32E162D0800A4C3FF /* RecognizeSmallView.swift */,
				588685D42E162D0800A4C3FF /* RecognizeView.swift */,
			);
			path = Recognize;
			sourceTree = "<group>";
		};
		588685E02E162D0800A4C3FF /* Chat */ = {
			isa = PBXGroup;
			children = (
				58DD65252E405289007F49EC /* Components */,
				588685CD2E162D0800A4C3FF /* Archived */,
				588685D02E162D0800A4C3FF /* MarkDown */,
				588685D52E162D0800A4C3FF /* Recognize */,
				588685D62E162D0800A4C3FF /* ChatsListView.swift */,
				588685D72E162D0800A4C3FF /* ChatView.swift */,
				588685D82E162D0800A4C3FF /* ChatViewPreview.swift */,
				588685D92E162D0800A4C3FF /* MessageBubble.swift */,
				588685DA2E162D0800A4C3FF /* OptimizedMessageView.swift */,
				588685DB2E162D0800A4C3FF /* ScrollPositionReader.swift */,
				588685DC2E162D0800A4C3FF /* ScrollStateManager.swift */,
				588685DD2E162D0800A4C3FF /* SideMenuModifier.swift */,
				588685DE2E162D0800A4C3FF /* TextSelectionView.swift */,
				588685DF2E162D0800A4C3FF /* TypingIndicator.swift */,
			);
			path = Chat;
			sourceTree = "<group>";
		};
		588685E32E162D0800A4C3FF /* Components */ = {
			isa = PBXGroup;
			children = (
				588685E12E162D0800A4C3FF /* EmptySessionView.swift */,
				588685E22E162D0800A4C3FF /* EnhancedLoadingView.swift */,
			);
			path = Components;
			sourceTree = "<group>";
		};
		588685E72E162D0800A4C3FF /* InputView */ = {
			isa = PBXGroup;
			children = (
				588685E42E162D0800A4C3FF /* InputBottomView.swift */,
				588685E52E162D0800A4C3FF /* InputField.swift */,
				588685E62E162D0800A4C3FF /* InputViewModel.swift */,
			);
			path = InputView;
			sourceTree = "<group>";
		};
		588685EE2E162D0800A4C3FF /* Login */ = {
			isa = PBXGroup;
			children = (
				588685E82E162D0800A4C3FF /* EmailLoginView.swift */,
				588685E92E162D0800A4C3FF /* InputingAnimationView.swift */,
				588685EA2E162D0800A4C3FF /* LoginView.swift */,
				588685EB2E162D0800A4C3FF /* PinCodeInputView.swift */,
				588685EC2E162D0800A4C3FF /* RegisterView.swift */,
				588685ED2E162D0800A4C3FF /* VerificationView.swift */,
			);
			path = Login;
			sourceTree = "<group>";
		};
		588685F12E162D0800A4C3FF /* Main */ = {
			isa = PBXGroup;
			children = (
				588685EF2E162D0800A4C3FF /* ContentView.swift */,
				588685F02E162D0800A4C3FF /* SideMenu.swift */,
			);
			path = Main;
			sourceTree = "<group>";
		};
		588685FA2E162D0800A4C3FF /* Setting */ = {
			isa = PBXGroup;
			children = (
				588685F22E162D0800A4C3FF /* AboutView.swift */,
				588685F32E162D0800A4C3FF /* AppListView.swift */,
				588685F42E162D0800A4C3FF /* PreferLanguageView.swift */,
				588685F52E162D0800A4C3FF /* PricingView.swift */,
				588685F62E162D0800A4C3FF /* PurchaseView.swift */,
				588685F72E162D0800A4C3FF /* RechargeView.swift */,
				588685F82E162D0800A4C3FF /* SettingsView.swift */,
				588685F92E162D0800A4C3FF /* WebView.swift */,
			);
			path = Setting;
			sourceTree = "<group>";
		};
		588685FC2E162D0800A4C3FF /* Splash */ = {
			isa = PBXGroup;
			children = (
				588685FB2E162D0800A4C3FF /* SplashView.swift */,
			);
			path = Splash;
			sourceTree = "<group>";
		};
		588686062E162D0800A4C3FF /* Step */ = {
			isa = PBXGroup;
			children = (
				58DD65322E4097C4007F49EC /* StepEditSection.swift */,
				58DD65332E4097C4007F49EC /* UnifiedStepFormView.swift */,
				588685FD2E162D0800A4C3FF /* ChatConfigDatabaseManager.swift */,
				588685FE2E162D0800A4C3FF /* MultiStepFormPreviewView.swift */,
				588685FF2E162D0800A4C3FF /* MultiStepFormView.swift */,
				588686002E162D0800A4C3FF /* MultiStepFormViewModel.swift */,
				588686012E162D0800A4C3FF /* PredefinedOptionKey.swift */,
				588686022E162D0800A4C3FF /* StepPreviewView.swift */,
				588686032E162D0800A4C3FF /* StepProtocol.swift */,
				588686042E162D0800A4C3FF /* StepView.swift */,
				588686052E162D0800A4C3FF /* StepViewModel.swift */,
			);
			path = Step;
			sourceTree = "<group>";
		};
		588686092E162D0800A4C3FF /* Views */ = {
			isa = PBXGroup;
			children = (
				58DD65062E404CBE007F49EC /* Startup */,
				588685E02E162D0800A4C3FF /* Chat */,
				588685E32E162D0800A4C3FF /* Components */,
				588685E72E162D0800A4C3FF /* InputView */,
				588685EE2E162D0800A4C3FF /* Login */,
				588685F12E162D0800A4C3FF /* Main */,
				588685FA2E162D0800A4C3FF /* Setting */,
				588685FC2E162D0800A4C3FF /* Splash */,
				588686062E162D0800A4C3FF /* Step */,
				588686072E162D0800A4C3FF /* MessageBubblePreview.swift */,
				588686082E162D0800A4C3FF /* UpdatePromptView.swift */,
			);
			path = Views;
			sourceTree = "<group>";
		};
		58DD65062E404CBE007F49EC /* Startup */ = {
			isa = PBXGroup;
			children = (
				58DD65052E404CBE007F49EC /* StartupLoadingView.swift */,
			);
			path = Startup;
			sourceTree = "<group>";
		};
		58DD65142E405271007F49EC /* Implementation */ = {
			isa = PBXGroup;
			children = (
				58DD65122E405271007F49EC /* OptimizedChatRepository.swift */,
				58DD65132E405271007F49EC /* OptimizedMessageRepository.swift */,
			);
			path = Implementation;
			sourceTree = "<group>";
		};
		58DD65192E405271007F49EC /* Repository */ = {
			isa = PBXGroup;
			children = (
				58DD65142E405271007F49EC /* Implementation */,
			);
			path = Repository;
			sourceTree = "<group>";
		};
		58DD65252E405289007F49EC /* Components */ = {
			isa = PBXGroup;
			children = (
				58DD65242E405289007F49EC /* OptimizedChatRowView.swift */,
			);
			path = Components;
			sourceTree = "<group>";
		};
		58EDE1B02E38A40F00AB1BA8 /* Cache */ = {
			isa = PBXGroup;
			children = (
				58EDE1AF2E38A40F00AB1BA8 /* CacheManager.swift */,
			);
			path = Cache;
			sourceTree = "<group>";
		};
		58EDE1B22E38A40F00AB1BA8 /* Migrations */ = {
			isa = PBXGroup;
			children = (
				58EDE1B12E38A40F00AB1BA8 /* BaseMigration.swift */,
			);
			path = Migrations;
			sourceTree = "<group>";
		};
		58EDE1B72E38A40F00AB1BA8 /* Repository */ = {
			isa = PBXGroup;
			children = (
				58DD65282E405789007F49EC /* MessageRepositoryProtocol.swift */,
				58EDE1B32E38A40F00AB1BA8 /* ChatListRepository.swift */,
				58EDE1B42E38A40F00AB1BA8 /* ChatRepository.swift */,
				58EDE1B52E38A40F00AB1BA8 /* ChatRepositoryProtocol.swift */,
				58EDE1B62E38A40F00AB1BA8 /* MessageRepository.swift */,
			);
			path = Repository;
			sourceTree = "<group>";
		};
		58EDE1BD2E38A40F00AB1BA8 /* DataBase */ = {
			isa = PBXGroup;
			children = (
				58EDE1B02E38A40F00AB1BA8 /* Cache */,
				58EDE1B22E38A40F00AB1BA8 /* Migrations */,
				58EDE1B72E38A40F00AB1BA8 /* Repository */,
				58EDE1B82E38A40F00AB1BA8 /* AdvisorDatabaseManager.swift */,
				58EDE1B92E38A40F00AB1BA8 /* DatabaseIntegrityChecker.swift */,
				58EDE1BA2E38A40F00AB1BA8 /* DatabaseVersion.swift */,
				58EDE1BB2E38A40F00AB1BA8 /* DataChangeNotificationCenter.swift */,
			);
			path = DataBase;
			sourceTree = "<group>";
		};
		58EDE1BF2E38A40F00AB1BA8 /* Debug */ = {
			isa = PBXGroup;
			children = (
				58EDE1BE2E38A40F00AB1BA8 /* DataFlowDebugger.swift */,
			);
			path = Debug;
			sourceTree = "<group>";
		};
		58EDE1C12E38A40F00AB1BA8 /* Factory */ = {
			isa = PBXGroup;
			children = (
				58EDE1C02E38A40F00AB1BA8 /* ChatListComponentFactory.swift */,
			);
			path = Factory;
			sourceTree = "<group>";
		};
		58EDE1CD2E38A40F00AB1BA8 /* Network */ = {
			isa = PBXGroup;
			children = (
				58EDE1C22E38A40F00AB1BA8 /* APICommom.swift */,
				58EDE1C32E38A40F00AB1BA8 /* AuthTarget.swift */,
				58EDE1C42E38A40F00AB1BA8 /* ChatTarget.swift */,
				58EDE1C52E38A40F00AB1BA8 /* ConfigTarget.swift */,
				58EDE1C62E38A40F00AB1BA8 /* ConnectionManager.swift */,
				58EDE1C72E38A40F00AB1BA8 /* ErrorRecoveryManager.swift */,
				58EDE1C82E38A40F00AB1BA8 /* NetworkService.swift */,
				58EDE1C92E38A40F00AB1BA8 /* NetworkStatusManager.swift */,
				58EDE1CA2E38A40F00AB1BA8 /* PricingTarget.swift */,
				58EDE1CB2E38A40F00AB1BA8 /* PurchaseTarget.swift */,
				58EDE1CC2E38A40F00AB1BA8 /* UnifiedSSEManager.swift */,
			);
			path = Network;
			sourceTree = "<group>";
		};
		58EDE1D92E38A40F00AB1BA8 /* Service */ = {
			isa = PBXGroup;
			children = (
				58DD652B2E405E76007F49EC /* APIRequestManager.swift */,
				58DD65192E405271007F49EC /* Repository */,
				58DD65022E404CB1007F49EC /* AppStartupManager.swift */,
				58EDE1BD2E38A40F00AB1BA8 /* DataBase */,
				58EDE1BF2E38A40F00AB1BA8 /* Debug */,
				58EDE1C12E38A40F00AB1BA8 /* Factory */,
				58EDE1CD2E38A40F00AB1BA8 /* Network */,
				58EDE1D02E38A40F00AB1BA8 /* AccountManager.swift */,
				58EDE1D12E38A40F00AB1BA8 /* BootsManager.swift */,
				58EDE1D22E38A40F00AB1BA8 /* ChatSessionManager.swift */,
				58EDE1D32E38A40F00AB1BA8 /* MessageLoader.swift */,
				58EDE1D42E38A40F00AB1BA8 /* Preferences.swift */,
				58EDE1D52E38A40F00AB1BA8 /* ShopManager.swift */,
				58EDE1D62E38A40F00AB1BA8 /* TypewriterEffectManager.swift */,
				58EDE1D72E38A40F00AB1BA8 /* UserSettings.swift */,
				58EDE1D82E38A40F00AB1BA8 /* VersionUpdateManager.swift */,
			);
			path = Service;
			sourceTree = "<group>";
		};
		58EDE2202E38A42C00AB1BA8 /* Chat */ = {
			isa = PBXGroup;
			children = (
				58EDE21E2E38A42C00AB1BA8 /* ChatUIStateManager.swift */,
				58EDE21F2E38A42C00AB1BA8 /* MessageManager.swift */,
			);
			path = Chat;
			sourceTree = "<group>";
		};
		58EDE2252E38A42C00AB1BA8 /* ChatList */ = {
			isa = PBXGroup;
			children = (
				58EDE2212E38A42C00AB1BA8 /* ChatListCoordinator.swift */,
				58EDE2222E38A42C00AB1BA8 /* ChatListDataSource.swift */,
				58EDE2232E38A42C00AB1BA8 /* ChatListUIStateManager.swift */,
				58EDE2242E38A42C00AB1BA8 /* RefactoredChatListViewModel.swift */,
			);
			path = ChatList;
			sourceTree = "<group>";
		};
		58EDE2302E38A42C00AB1BA8 /* ViewModel */ = {
			isa = PBXGroup;
			children = (
				58EDE2202E38A42C00AB1BA8 /* Chat */,
				58EDE2252E38A42C00AB1BA8 /* ChatList */,
				58EDE2262E38A42C00AB1BA8 /* AuthViewModel.swift */,
				58EDE2272E38A42C00AB1BA8 /* ChatListViewModel.swift */,
				58EDE2282E38A42C00AB1BA8 /* ChatViewModel.swift */,
				58EDE2292E38A42C00AB1BA8 /* ContentViewModel.swift */,
				58EDE22A2E38A42C00AB1BA8 /* PricingViewModel.swift */,
				58EDE22B2E38A42C00AB1BA8 /* PurchaseViewModel.swift */,
				58EDE22C2E38A42C00AB1BA8 /* RecognizeViewModel.swift */,
				58EDE22D2E38A42C00AB1BA8 /* RegisterViewModel.swift */,
				58EDE22E2E38A42C00AB1BA8 /* SplashViewModel.swift */,
				58EDE22F2E38A42C00AB1BA8 /* VerificationViewModel.swift */,
			);
			path = ViewModel;
			sourceTree = "<group>";
		};
		6C17C4DD2C09708D005BA6A3 /* i18n */ = {
			isa = PBXGroup;
			children = (
				D6DC913D2C0C6C1F009DB0AB /* Localizable.strings */,
				6CA36DE02C1833EB00AE82B2 /* InfoPlist.strings */,
			);
			path = i18n;
			sourceTree = "<group>";
		};
		9706CC5D2BC3CFA40080FECE = {
			isa = PBXGroup;
			children = (
				9706CC682BC3CFA40080FECE /* ChatAdvisor */,
				9706CC792BC3CFA80080FECE /* JunShiTests */,
				9706CC832BC3CFA90080FECE /* JunShiUITests */,
				9706CC672BC3CFA40080FECE /* Products */,
				D643402A2BF8D22000F62E35 /* Frameworks */,
			);
			sourceTree = "<group>";
		};
		9706CC672BC3CFA40080FECE /* Products */ = {
			isa = PBXGroup;
			children = (
				9706CC662BC3CFA40080FECE /* ChatAdvisor.app */,
				9706CC762BC3CFA80080FECE /* ChatAdvisorTests.xctest */,
				9706CC802BC3CFA90080FECE /* ChatAdvisorUITests.xctest */,
				9706CCB72BC3F2730080FECE /* ChatAdvisor Debug.app */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		9706CC682BC3CFA40080FECE /* ChatAdvisor */ = {
			isa = PBXGroup;
			children = (
				6C17C4DD2C09708D005BA6A3 /* i18n */,
				6CE61D712BF3041900B8B4F1 /* PrivacyInfo.xcprivacy */,
				6CE61D702BF3001600B8B4F1 /* ChatAdvisor.entitlements */,
				6C17C4E12C09715B005BA6A3 /* Info.plist */,
				9777D11F2BC7DB4C00619386 /* Sources */,
				9706CC9F2BC3ED460080FECE /* GoogleService-Info.plist */,
				9706CC692BC3CFA40080FECE /* ChatAdvisorApp.swift */,
				9706CC6D2BC3CFA80080FECE /* Assets.xcassets */,
				9706CC6F2BC3CFA80080FECE /* Preview Content */,
				6C0310782CDC6DDF00E0C305 /* AppDelegate.swift */,
			);
			path = ChatAdvisor;
			sourceTree = "<group>";
		};
		9706CC6F2BC3CFA80080FECE /* Preview Content */ = {
			isa = PBXGroup;
			children = (
				9706CC702BC3CFA80080FECE /* Preview Assets.xcassets */,
			);
			path = "Preview Content";
			sourceTree = "<group>";
		};
		9706CC792BC3CFA80080FECE /* JunShiTests */ = {
			isa = PBXGroup;
			children = (
				9706CC7A2BC3CFA80080FECE /* JunShiTests.swift */,
			);
			path = JunShiTests;
			sourceTree = "<group>";
		};
		9706CC832BC3CFA90080FECE /* JunShiUITests */ = {
			isa = PBXGroup;
			children = (
				9706CC842BC3CFA90080FECE /* JunShiUITests.swift */,
				9706CC862BC3CFA90080FECE /* JunShiUITestsLaunchTests.swift */,
			);
			path = JunShiUITests;
			sourceTree = "<group>";
		};
		9777D11F2BC7DB4C00619386 /* Sources */ = {
			isa = PBXGroup;
			children = (
				58EDE2302E38A42C00AB1BA8 /* ViewModel */,
				58EDE1D92E38A40F00AB1BA8 /* Service */,
				D6C63CB12BCD6A420004BF5E /* Model */,
				D6C63C9E2BCD6A420004BF5E /* Utility */,
				588686092E162D0800A4C3FF /* Views */,
			);
			path = Sources;
			sourceTree = "<group>";
		};
		D643402A2BF8D22000F62E35 /* Frameworks */ = {
			isa = PBXGroup;
			children = (
				6C18B8372CC5EF660029CB87 /* AdSupport.framework */,
				D65D5CDF2C232D55004504BE /* libc++.tbd */,
				D65D5CDE2C232D4A004504BE /* libz.tbd */,
				D65D5CDC2C232D34004504BE /* Security.framework */,
				D65D5CDA2C232D2B004504BE /* SystemConfiguration.framework */,
				D65D5CD82C232D14004504BE /* Bugly.framework */,
				D643402B2BF8D22000F62E35 /* StoreKit.framework */,
			);
			name = Frameworks;
			sourceTree = "<group>";
		};
		D6C63C9E2BCD6A420004BF5E /* Utility */ = {
			isa = PBXGroup;
			children = (
				D6C63C9D2BCD6A420004BF5E /* CryptoFunction.swift */,
				6CA0CEA72BD24B8600A6B32B /* StringExtension.swift */,
				6CA0CEAC2BD251EB00A6B32B /* ViewExtension.swift */,
				6C9739622BDE2E5900F53F1D /* AppThemes.swift */,
				6CE61D462BEC7D8B00B8B4F1 /* DateExtension .swift */,
				6C0CBB0E2BF756DB009036A5 /* WeakList.swift */,
				6C17C4DB2C083970005BA6A3 /* VibrationController.swift */,
				D6DC914A2C1205B3009DB0AB /* UIDeviceExtension.swift */,
				6C6690BB2C2EDA6B00FA97E1 /* ColorExtension.swift */,
				6C6690BD2C2EDA9500FA97E1 /* UserDefaultsExtension.swift */,
				6C6690BF2C2EE46000FA97E1 /* FirebaseManager.swift */,
				6C62A6C12C465639002D40D5 /* AppReviewManager.swift */,
				6C1FCBAD2C5724E400706697 /* URLExtension.swift */,
			);
			path = Utility;
			sourceTree = "<group>";
		};
		D6C63CB12BCD6A420004BF5E /* Model */ = {
			isa = PBXGroup;
			children = (
				D61300652BF3B561006C86C0 /* ChatsModel.swift */,
				D6C63CAE2BCD6A420004BF5E /* Chats.swift */,
				D6C63CAF2BCD6A420004BF5E /* NetworkResponse.swift */,
				D6C63CB02BCD6A420004BF5E /* User.swift */,
				6C3000D42BDF70DE0079AF38 /* OpenAIModel.swift */,
				D64340262BF8C33B00F62E35 /* Balance.swift */,
				6C17C4E72C09BDAD005BA6A3 /* Product.swift */,
				D62099602C0B8731000178CF /* Configs.swift */,
				6C6E215D2C364B1D0081A7D6 /* RecognizedText.swift */,
			);
			path = Model;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXNativeTarget section */
		9706CC652BC3CFA40080FECE /* ChatAdvisor */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 9706CC8A2BC3CFA90080FECE /* Build configuration list for PBXNativeTarget "ChatAdvisor" */;
			buildPhases = (
				9706CC622BC3CFA40080FECE /* Sources */,
				9706CC642BC3CFA40080FECE /* Resources */,
				9706CC632BC3CFA40080FECE /* Frameworks */,
				9706CCBB2BC3F8D20080FECE /* FirebaseUpload */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = ChatAdvisor;
			packageProductDependencies = (
				9706CC9D2BC3ECB50080FECE /* FirebaseAnalytics */,
				D6C84FE52BC436D0005FBCD6 /* FirebaseCrashlytics */,
				9777D1042BC7DAEE00619386 /* Moya */,
				9777D1072BC7DB3200619386 /* Alamofire */,
				6CC844272BD2096A00B4698C /* KeychainSwift */,
				6CC844322BD21B8800B4698C /* Localize_Swift */,
				6CA0CEAA2BD24C1C00A6B32B /* SwifterSwift */,
				6C9739602BDE2DFB00F53F1D /* LookinServer */,
				6CFEAFFF2BE0DFD70046C53A /* WCDBSwift */,
				6C3987372BEB26CF0026E28C /* LDSwiftEventSource */,
				6CE61D672BEF02D400B8B4F1 /* SimpleToast */,
				6CDA93F72BF483EA00C52DDF /* MarkdownUI */,
				6CD675302BF5EB58008134B8 /* Splash */,
				D66B674E2C2B03EB00DB7598 /* GoogleSignInSwift */,
				6C6E214C2C33B2170081A7D6 /* FirebaseMessaging */,
				6C71C88C2C3CCD020001B917 /* StepperView */,
				6C62A6C42C48C7AC002D40D5 /* EventSource */,
				6C4529D02C52262100489F07 /* FacebookCore */,
				6C4529D22C52262100489F07 /* FacebookLogin */,
				6C90D6952CE763A600AD016E /* TikTokOpenSDKCore */,
				6C90D6972CE7645300AD016E /* TikTokOpenAuthSDK */,
			);
			productName = JunShi;
			productReference = 9706CC662BC3CFA40080FECE /* ChatAdvisor.app */;
			productType = "com.apple.product-type.application";
		};
		9706CC752BC3CFA80080FECE /* ChatAdvisorTests */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 9706CC8D2BC3CFA90080FECE /* Build configuration list for PBXNativeTarget "ChatAdvisorTests" */;
			buildPhases = (
				9706CC722BC3CFA80080FECE /* Sources */,
				9706CC732BC3CFA80080FECE /* Frameworks */,
				9706CC742BC3CFA80080FECE /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				9706CC782BC3CFA80080FECE /* PBXTargetDependency */,
			);
			name = ChatAdvisorTests;
			productName = JunShiTests;
			productReference = 9706CC762BC3CFA80080FECE /* ChatAdvisorTests.xctest */;
			productType = "com.apple.product-type.bundle.unit-test";
		};
		9706CC7F2BC3CFA90080FECE /* ChatAdvisorUITests */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 9706CC902BC3CFA90080FECE /* Build configuration list for PBXNativeTarget "ChatAdvisorUITests" */;
			buildPhases = (
				9706CC7C2BC3CFA90080FECE /* Sources */,
				9706CC7D2BC3CFA90080FECE /* Frameworks */,
				9706CC7E2BC3CFA90080FECE /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				9706CC822BC3CFA90080FECE /* PBXTargetDependency */,
			);
			name = ChatAdvisorUITests;
			productName = JunShiUITests;
			productReference = 9706CC802BC3CFA90080FECE /* ChatAdvisorUITests.xctest */;
			productType = "com.apple.product-type.bundle.ui-testing";
		};
		9706CCA32BC3F2730080FECE /* ChatAdvisor Debug */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 9706CCB42BC3F2730080FECE /* Build configuration list for PBXNativeTarget "ChatAdvisor Debug" */;
			buildPhases = (
				9706CCA82BC3F2730080FECE /* Sources */,
				9706CCAB2BC3F2730080FECE /* Frameworks */,
				9706CCAF2BC3F2730080FECE /* Resources */,
				9706CCB32BC3F2730080FECE /* ShellScript */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = "ChatAdvisor Debug";
			packageProductDependencies = (
				9706CCA42BC3F2730080FECE /* JunShiPackage */,
				9706CCA52BC3F2730080FECE /* JunShiPackage */,
				9706CCA62BC3F2730080FECE /* FirebaseAnalytics */,
			);
			productName = JunShi;
			productReference = 9706CCB72BC3F2730080FECE /* ChatAdvisor Debug.app */;
			productType = "com.apple.product-type.application";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		9706CC5E2BC3CFA40080FECE /* Project object */ = {
			isa = PBXProject;
			attributes = {
				BuildIndependentTargetsInParallel = 1;
				LastSwiftUpdateCheck = 1530;
				LastUpgradeCheck = 1540;
				TargetAttributes = {
					9706CC652BC3CFA40080FECE = {
						CreatedOnToolsVersion = 15.3;
					};
					9706CC752BC3CFA80080FECE = {
						CreatedOnToolsVersion = 15.3;
						TestTargetID = 9706CC652BC3CFA40080FECE;
					};
					9706CC7F2BC3CFA90080FECE = {
						CreatedOnToolsVersion = 15.3;
						TestTargetID = 9706CC652BC3CFA40080FECE;
					};
				};
			};
			buildConfigurationList = 9706CC612BC3CFA40080FECE /* Build configuration list for PBXProject "ChatAdvisor" */;
			compatibilityVersion = "Xcode 14.0";
			developmentRegion = en;
			hasScannedForEncodings = 0;
			knownRegions = (
				en,
				Base,
				"zh-Hans",
				vi,
				th,
				tr,
				uk,
				ar,
				ja,
				ru,
				ga,
				"zh-HK",
				"zh-Hant",
				lo,
				ms,
				es,
				de,
				ca,
				fr,
				it,
				ko,
				pl,
				"es-419",
				"pt-BR",
				hr,
				cs,
				da,
				nl,
				fi,
				el,
				he,
				hi,
				hu,
				id,
				nb,
				ro,
				sk,
				sv,
				ab,
				"en-AU",
				"en-IN",
				"en-GB",
				"fr-CA",
				"pt-PT",
				fil,
				fa,
				"ur-PK",
				ps,
				"fr-CI",
			);
			mainGroup = 9706CC5D2BC3CFA40080FECE;
			packageReferences = (
				9706CC9C2BC3ECB50080FECE /* XCRemoteSwiftPackageReference "firebase-ios-sdk" */,
				9777D1032BC7DAEE00619386 /* XCRemoteSwiftPackageReference "Moya" */,
				9777D1062BC7DB3200619386 /* XCRemoteSwiftPackageReference "Alamofire" */,
				6CC844262BD2096A00B4698C /* XCRemoteSwiftPackageReference "keychain-swift" */,
				6CC844312BD21B8800B4698C /* XCRemoteSwiftPackageReference "Localize-Swift" */,
				6CA0CEA92BD24C1C00A6B32B /* XCRemoteSwiftPackageReference "SwifterSwift" */,
				6C97395F2BDE2DFB00F53F1D /* XCRemoteSwiftPackageReference "LookinServer" */,
				6CFEAFFE2BE0DFD70046C53A /* XCRemoteSwiftPackageReference "wcdb" */,
				6C3987362BEB26CF0026E28C /* XCRemoteSwiftPackageReference "swift-eventsource" */,
				6CE61D662BEF02D400B8B4F1 /* XCRemoteSwiftPackageReference "SimpleToast" */,
				6CDA93F62BF483EA00C52DDF /* XCRemoteSwiftPackageReference "swift-markdown-ui" */,
				6CD6752F2BF5EB58008134B8 /* XCRemoteSwiftPackageReference "Splash" */,
				D66B674D2C2B03EB00DB7598 /* XCRemoteSwiftPackageReference "GoogleSignIn-iOS" */,
				6C71C88B2C3CCD020001B917 /* XCRemoteSwiftPackageReference "StepperView" */,
				6C62A6C32C48C7AC002D40D5 /* XCRemoteSwiftPackageReference "EventSource" */,
				6C4529CF2C52262100489F07 /* XCRemoteSwiftPackageReference "facebook-ios-sdk" */,
				6C90D6942CE763A600AD016E /* XCRemoteSwiftPackageReference "tiktok-opensdk-ios" */,
			);
			productRefGroup = 9706CC672BC3CFA40080FECE /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				9706CC652BC3CFA40080FECE /* ChatAdvisor */,
				9706CC752BC3CFA80080FECE /* ChatAdvisorTests */,
				9706CC7F2BC3CFA90080FECE /* ChatAdvisorUITests */,
				9706CCA32BC3F2730080FECE /* ChatAdvisor Debug */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		9706CC642BC3CFA40080FECE /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				9706CC712BC3CFA80080FECE /* Preview Assets.xcassets in Resources */,
				6CA36DE22C1833EB00AE82B2 /* InfoPlist.strings in Resources */,
				D6DC913F2C0C6C1F009DB0AB /* Localizable.strings in Resources */,
				6C17C4E62C09740F005BA6A3 /* Info.plist in Resources */,
				9706CC6E2BC3CFA80080FECE /* Assets.xcassets in Resources */,
				6CE61D722BF3041A00B8B4F1 /* PrivacyInfo.xcprivacy in Resources */,
				9706CCBC2BC4018A0080FECE /* GoogleService-Info.plist in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		9706CC742BC3CFA80080FECE /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		9706CC7E2BC3CFA90080FECE /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		9706CCAF2BC3F2730080FECE /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				9706CCB02BC3F2730080FECE /* Preview Assets.xcassets in Resources */,
				9706CCB12BC3F2730080FECE /* Assets.xcassets in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXShellScriptBuildPhase section */
		9706CCB32BC3F2730080FECE /* ShellScript */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputFileListPaths = (
			);
			inputPaths = (
				"${DWARF_DSYM_FOLDER_PATH}/${DWARF_DSYM_FILE_NAME}/Contents/Resources/DWARF/${TARGET_NAME}",
				"$(SRCROOT)/$(BUILT_PRODUCTS_DIR)/$(INFOPLIST_PATH)",
			);
			outputFileListPaths = (
			);
			outputPaths = (
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "# Type a script or drag a script file from your workspace to insert its path.\n\"${BUILD_DIR%/Build/*}/SourcePackages/checkouts/firebase-ios-sdk/Crashlytics/run\"\n";
		};
		9706CCBB2BC3F8D20080FECE /* FirebaseUpload */ = {
			isa = PBXShellScriptBuildPhase;
			alwaysOutOfDate = 1;
			buildActionMask = 2147483647;
			files = (
			);
			inputFileListPaths = (
			);
			inputPaths = (
				"${DWARF_DSYM_FOLDER_PATH}/${DWARF_DSYM_FILE_NAME}/Contents/Resources/DWARF/${TARGET_NAME}",
				"$(SRCROOT)/ChatAdivosr/GoogleService-Info.plist",
			);
			name = FirebaseUpload;
			outputFileListPaths = (
			);
			outputPaths = (
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "if [ \"${CONFIGURATION}\" = \"Release\" ]; then\n    \"${BUILD_DIR%/Build/*}/SourcePackages/checkouts/firebase-ios-sdk/Crashlytics/upload-symbols\" -gsp \"${SRCROOT}/ChatAdvisor/GoogleService-Info.plist\" -p ios \"${DWARF_DSYM_FOLDER_PATH}/${DWARF_DSYM_FILE_NAME}\"\nfi\necho \"${BUILD_DIR%/Build/*}/SourcePackages/checkouts/firebase-ios-sdk/Crashlytics/upload-symbols\" \necho \"${SRCROOT}/ChatAdvisor/GoogleService-Info.plist\"\necho \"${DWARF_DSYM_FOLDER_PATH}/${DWARF_DSYM_FILE_NAME}\"\n";
		};
/* End PBXShellScriptBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		9706CC622BC3CFA40080FECE /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				58DD65292E405789007F49EC /* MessageRepositoryProtocol.swift in Sources */,
				6C0310792CDC6DDF00E0C305 /* AppDelegate.swift in Sources */,
				6CE61D472BEC7D8B00B8B4F1 /* DateExtension .swift in Sources */,
				58EDE1DA2E38A40F00AB1BA8 /* DataChangeNotificationCenter.swift in Sources */,
				58DD651B2E405271007F49EC /* OptimizedChatRepository.swift in Sources */,
				58DD651E2E405271007F49EC /* OptimizedMessageRepository.swift in Sources */,
				58EDE1DC2E38A40F00AB1BA8 /* DataFlowDebugger.swift in Sources */,
				58EDE1DD2E38A40F00AB1BA8 /* AccountManager.swift in Sources */,
				58EDE1DE2E38A40F00AB1BA8 /* NetworkStatusManager.swift in Sources */,
				58EDE1DF2E38A40F00AB1BA8 /* ChatTarget.swift in Sources */,
				58EDE1E02E38A40F00AB1BA8 /* VersionUpdateManager.swift in Sources */,
				58EDE1E12E38A40F00AB1BA8 /* ChatListRepository.swift in Sources */,
				58EDE1E22E38A40F00AB1BA8 /* APICommom.swift in Sources */,
				58EDE1E32E38A40F00AB1BA8 /* CacheManager.swift in Sources */,
				58EDE1E42E38A40F00AB1BA8 /* TypewriterEffectManager.swift in Sources */,
				58EDE1E52E38A40F00AB1BA8 /* DatabaseVersion.swift in Sources */,
				58EDE1E62E38A40F00AB1BA8 /* ChatRepositoryProtocol.swift in Sources */,
				58EDE1E72E38A40F00AB1BA8 /* ConnectionManager.swift in Sources */,
				58EDE1E82E38A40F00AB1BA8 /* DatabaseIntegrityChecker.swift in Sources */,
				58EDE1E92E38A40F00AB1BA8 /* MessageLoader.swift in Sources */,
				58EDE1EA2E38A40F00AB1BA8 /* UserSettings.swift in Sources */,
				58EDE1EB2E38A40F00AB1BA8 /* MessageRepository.swift in Sources */,
				58EDE1EC2E38A40F00AB1BA8 /* NetworkService.swift in Sources */,
				58EDE1ED2E38A40F00AB1BA8 /* PricingTarget.swift in Sources */,
				58EDE1EF2E38A40F00AB1BA8 /* ChatRepository.swift in Sources */,
				58EDE1F02E38A40F00AB1BA8 /* ChatSessionManager.swift in Sources */,
				58EDE1F12E38A40F00AB1BA8 /* ConfigTarget.swift in Sources */,
				58EDE1F22E38A40F00AB1BA8 /* AuthTarget.swift in Sources */,
				58EDE1F32E38A40F00AB1BA8 /* BootsManager.swift in Sources */,
				58EDE1F42E38A40F00AB1BA8 /* ChatListComponentFactory.swift in Sources */,
				58EDE1F52E38A40F00AB1BA8 /* PurchaseTarget.swift in Sources */,
				58EDE1F62E38A40F00AB1BA8 /* UnifiedSSEManager.swift in Sources */,
				58EDE1F72E38A40F00AB1BA8 /* Preferences.swift in Sources */,
				58EDE1F82E38A40F00AB1BA8 /* AdvisorDatabaseManager.swift in Sources */,
				58EDE1F92E38A40F00AB1BA8 /* ShopManager.swift in Sources */,
				58EDE1FA2E38A40F00AB1BA8 /* BaseMigration.swift in Sources */,
				58EDE1FB2E38A40F00AB1BA8 /* ErrorRecoveryManager.swift in Sources */,
				D6DC914B2C1205B3009DB0AB /* UIDeviceExtension.swift in Sources */,
				6C1FCBAE2C5724E400706697 /* URLExtension.swift in Sources */,
				6C0CBB0F2BF756DB009036A5 /* WeakList.swift in Sources */,
				D61300662BF3B561006C86C0 /* ChatsModel.swift in Sources */,
				D6C63CBC2BCD6A420004BF5E /* Chats.swift in Sources */,
				9706CC6A2BC3CFA40080FECE /* ChatAdvisorApp.swift in Sources */,
				6C6690C02C2EE46000FA97E1 /* FirebaseManager.swift in Sources */,
				D6C63CBE2BCD6A420004BF5E /* User.swift in Sources */,
				6C62A6C22C465639002D40D5 /* AppReviewManager.swift in Sources */,
				58EDE2312E38A42C00AB1BA8 /* ChatListUIStateManager.swift in Sources */,
				58EDE2322E38A42C00AB1BA8 /* ContentViewModel.swift in Sources */,
				58EDE2332E38A42C00AB1BA8 /* AuthViewModel.swift in Sources */,
				58EDE2342E38A42C00AB1BA8 /* SplashViewModel.swift in Sources */,
				58EDE2352E38A42C00AB1BA8 /* ChatViewModel.swift in Sources */,
				58EDE2362E38A42C00AB1BA8 /* ChatListCoordinator.swift in Sources */,
				58EDE2372E38A42C00AB1BA8 /* ChatListViewModel.swift in Sources */,
				58EDE2382E38A42C00AB1BA8 /* VerificationViewModel.swift in Sources */,
				58EDE2392E38A42C00AB1BA8 /* PricingViewModel.swift in Sources */,
				58EDE23A2E38A42C00AB1BA8 /* RecognizeViewModel.swift in Sources */,
				58EDE23B2E38A42C00AB1BA8 /* ChatListDataSource.swift in Sources */,
				58EDE23C2E38A42C00AB1BA8 /* RegisterViewModel.swift in Sources */,
				58EDE23D2E38A42C00AB1BA8 /* MessageManager.swift in Sources */,
				58EDE23E2E38A42C00AB1BA8 /* ChatUIStateManager.swift in Sources */,
				58EDE23F2E38A42C00AB1BA8 /* RefactoredChatListViewModel.swift in Sources */,
				58EDE2402E38A42C00AB1BA8 /* PurchaseViewModel.swift in Sources */,
				6C3000D52BDF70DE0079AF38 /* OpenAIModel.swift in Sources */,
				D6C63CBD2BCD6A420004BF5E /* NetworkResponse.swift in Sources */,
				D64340272BF8C33B00F62E35 /* Balance.swift in Sources */,
				58DD65042E404CB1007F49EC /* AppStartupManager.swift in Sources */,
				6C6E215E2C364B1D0081A7D6 /* RecognizedText.swift in Sources */,
				58DD652D2E405E76007F49EC /* APIRequestManager.swift in Sources */,
				6C6690BE2C2EDA9500FA97E1 /* UserDefaultsExtension.swift in Sources */,
				D6C63CB22BCD6A420004BF5E /* CryptoFunction.swift in Sources */,
				D62099612C0B8731000178CF /* Configs.swift in Sources */,
				6CA0CEAD2BD251EB00A6B32B /* ViewExtension.swift in Sources */,
				6C9739632BDE2E5900F53F1D /* AppThemes.swift in Sources */,
				6C6690BC2C2EDA6B00FA97E1 /* ColorExtension.swift in Sources */,
				6CA0CEA82BD24B8600A6B32B /* StringExtension.swift in Sources */,
				6C17C4E82C09BDAD005BA6A3 /* Product.swift in Sources */,
				5886860A2E162D0800A4C3FF /* ScrollStateManager.swift in Sources */,
				5886860B2E162D0800A4C3FF /* PredefinedOptionKey.swift in Sources */,
				5886860C2E162D0800A4C3FF /* ScrollPositionReader.swift in Sources */,
				5886860D2E162D0800A4C3FF /* PinCodeInputView.swift in Sources */,
				58DD65072E404CBE007F49EC /* StartupLoadingView.swift in Sources */,
				5886860E2E162D0800A4C3FF /* ChatsListView.swift in Sources */,
				5886860F2E162D0800A4C3FF /* WebView.swift in Sources */,
				588686102E162D0800A4C3FF /* RegisterView.swift in Sources */,
				588686112E162D0800A4C3FF /* ArchivedChatView.swift in Sources */,
				588686122E162D0800A4C3FF /* AboutView.swift in Sources */,
				588686132E162D0800A4C3FF /* RecognizeFullScreenView.swift in Sources */,
				588686142E162D0800A4C3FF /* RecognizeSmallView.swift in Sources */,
				588686152E162D0800A4C3FF /* RechargeView.swift in Sources */,
				588686162E162D0800A4C3FF /* SplashCodeSyntaxHighlighter.swift in Sources */,
				588686172E162D0800A4C3FF /* TypingIndicator.swift in Sources */,
				588686182E162D0800A4C3FF /* StepView.swift in Sources */,
				588686192E162D0800A4C3FF /* MultiStepFormViewModel.swift in Sources */,
				5886861A2E162D0800A4C3FF /* ArchivedChatViewModel.swift in Sources */,
				5886861B2E162D0800A4C3FF /* AppListView.swift in Sources */,
				5886861C2E162D0800A4C3FF /* SplashView.swift in Sources */,
				5886861D2E162D0800A4C3FF /* EmptySessionView.swift in Sources */,
				5886861E2E162D0800A4C3FF /* TextOutputFormat.swift in Sources */,
				5886861F2E162D0800A4C3FF /* UpdatePromptView.swift in Sources */,
				588686202E162D0800A4C3FF /* TextSelectionView.swift in Sources */,
				588686212E162D0800A4C3FF /* RecognizeView.swift in Sources */,
				588686222E162D0800A4C3FF /* InputViewModel.swift in Sources */,
				588686232E162D0800A4C3FF /* LoginView.swift in Sources */,
				588686242E162D0800A4C3FF /* EmailLoginView.swift in Sources */,
				58DD65342E4097C4007F49EC /* UnifiedStepFormView.swift in Sources */,
				58DD65362E4097C4007F49EC /* StepEditSection.swift in Sources */,
				588686252E162D0800A4C3FF /* OptimizedMessageView.swift in Sources */,
				588686262E162D0800A4C3FF /* MultiStepFormView.swift in Sources */,
				588686272E162D0800A4C3FF /* InputingAnimationView.swift in Sources */,
				588686282E162D0800A4C3FF /* EnhancedLoadingView.swift in Sources */,
				58DD65272E405289007F49EC /* OptimizedChatRowView.swift in Sources */,
				588686292E162D0800A4C3FF /* ChatConfigDatabaseManager.swift in Sources */,
				5886862A2E162D0800A4C3FF /* SideMenu.swift in Sources */,
				5886862B2E162D0800A4C3FF /* StepViewModel.swift in Sources */,
				5886862C2E162D0800A4C3FF /* ContentView.swift in Sources */,
				5886862D2E162D0800A4C3FF /* PreferLanguageView.swift in Sources */,
				5886862E2E162D0800A4C3FF /* RecognizeMessageView.swift in Sources */,
				5886862F2E162D0800A4C3FF /* InputBottomView.swift in Sources */,
				588686302E162D0800A4C3FF /* VerificationView.swift in Sources */,
				588686312E162D0800A4C3FF /* MessageBubblePreview.swift in Sources */,
				588686322E162D0800A4C3FF /* PurchaseView.swift in Sources */,
				588686332E162D0800A4C3FF /* SideMenuModifier.swift in Sources */,
				588686342E162D0800A4C3FF /* PricingView.swift in Sources */,
				588686352E162D0800A4C3FF /* ChatViewPreview.swift in Sources */,
				588686362E162D0800A4C3FF /* ArchivedChatsListView.swift in Sources */,
				588686372E162D0800A4C3FF /* StepProtocol.swift in Sources */,
				588686382E162D0800A4C3FF /* SettingsView.swift in Sources */,
				588686392E162D0800A4C3FF /* MessageBubble.swift in Sources */,
				5886863A2E162D0800A4C3FF /* InputField.swift in Sources */,
				5886863B2E162D0800A4C3FF /* MultiStepFormPreviewView.swift in Sources */,
				5886863C2E162D0800A4C3FF /* ChatView.swift in Sources */,
				5886863D2E162D0800A4C3FF /* StepPreviewView.swift in Sources */,
				6C17C4DC2C083970005BA6A3 /* VibrationController.swift in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		9706CC722BC3CFA80080FECE /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				9706CC7B2BC3CFA80080FECE /* JunShiTests.swift in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		9706CC7C2BC3CFA90080FECE /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				9706CC852BC3CFA90080FECE /* JunShiUITests.swift in Sources */,
				9706CC872BC3CFA90080FECE /* JunShiUITestsLaunchTests.swift in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		9706CCA82BC3F2730080FECE /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				5886863E2E162D0800A4C3FF /* ScrollStateManager.swift in Sources */,
				58EDE2412E38A42C00AB1BA8 /* ChatListUIStateManager.swift in Sources */,
				58EDE2422E38A42C00AB1BA8 /* ContentViewModel.swift in Sources */,
				58DD65372E4097C4007F49EC /* UnifiedStepFormView.swift in Sources */,
				58DD65392E4097C4007F49EC /* StepEditSection.swift in Sources */,
				58EDE2432E38A42C00AB1BA8 /* AuthViewModel.swift in Sources */,
				58EDE2442E38A42C00AB1BA8 /* SplashViewModel.swift in Sources */,
				58EDE2452E38A42C00AB1BA8 /* ChatViewModel.swift in Sources */,
				58EDE2462E38A42C00AB1BA8 /* ChatListCoordinator.swift in Sources */,
				58EDE2472E38A42C00AB1BA8 /* ChatListViewModel.swift in Sources */,
				58EDE2482E38A42C00AB1BA8 /* VerificationViewModel.swift in Sources */,
				58EDE2492E38A42C00AB1BA8 /* PricingViewModel.swift in Sources */,
				58EDE24A2E38A42C00AB1BA8 /* RecognizeViewModel.swift in Sources */,
				58EDE24B2E38A42C00AB1BA8 /* ChatListDataSource.swift in Sources */,
				58DD65202E405271007F49EC /* OptimizedChatRepository.swift in Sources */,
				58DD65232E405271007F49EC /* OptimizedMessageRepository.swift in Sources */,
				58EDE24C2E38A42C00AB1BA8 /* RegisterViewModel.swift in Sources */,
				58EDE24D2E38A42C00AB1BA8 /* MessageManager.swift in Sources */,
				58EDE24E2E38A42C00AB1BA8 /* ChatUIStateManager.swift in Sources */,
				58EDE24F2E38A42C00AB1BA8 /* RefactoredChatListViewModel.swift in Sources */,
				58EDE2502E38A42C00AB1BA8 /* PurchaseViewModel.swift in Sources */,
				5886863F2E162D0800A4C3FF /* PredefinedOptionKey.swift in Sources */,
				588686402E162D0800A4C3FF /* ScrollPositionReader.swift in Sources */,
				588686412E162D0800A4C3FF /* PinCodeInputView.swift in Sources */,
				588686422E162D0800A4C3FF /* ChatsListView.swift in Sources */,
				588686432E162D0800A4C3FF /* WebView.swift in Sources */,
				588686442E162D0800A4C3FF /* RegisterView.swift in Sources */,
				588686452E162D0800A4C3FF /* ArchivedChatView.swift in Sources */,
				588686462E162D0800A4C3FF /* AboutView.swift in Sources */,
				588686472E162D0800A4C3FF /* RecognizeFullScreenView.swift in Sources */,
				588686482E162D0800A4C3FF /* RecognizeSmallView.swift in Sources */,
				588686492E162D0800A4C3FF /* RechargeView.swift in Sources */,
				5886864A2E162D0800A4C3FF /* SplashCodeSyntaxHighlighter.swift in Sources */,
				5886864B2E162D0800A4C3FF /* TypingIndicator.swift in Sources */,
				5886864C2E162D0800A4C3FF /* StepView.swift in Sources */,
				5886864D2E162D0800A4C3FF /* MultiStepFormViewModel.swift in Sources */,
				58DD65032E404CB1007F49EC /* AppStartupManager.swift in Sources */,
				5886864E2E162D0800A4C3FF /* ArchivedChatViewModel.swift in Sources */,
				5886864F2E162D0800A4C3FF /* AppListView.swift in Sources */,
				588686502E162D0800A4C3FF /* SplashView.swift in Sources */,
				588686512E162D0800A4C3FF /* EmptySessionView.swift in Sources */,
				588686522E162D0800A4C3FF /* TextOutputFormat.swift in Sources */,
				588686532E162D0800A4C3FF /* UpdatePromptView.swift in Sources */,
				588686542E162D0800A4C3FF /* TextSelectionView.swift in Sources */,
				588686552E162D0800A4C3FF /* RecognizeView.swift in Sources */,
				588686562E162D0800A4C3FF /* InputViewModel.swift in Sources */,
				588686572E162D0800A4C3FF /* LoginView.swift in Sources */,
				58EDE1FC2E38A40F00AB1BA8 /* DataChangeNotificationCenter.swift in Sources */,
				58EDE1FE2E38A40F00AB1BA8 /* DataFlowDebugger.swift in Sources */,
				58EDE1FF2E38A40F00AB1BA8 /* AccountManager.swift in Sources */,
				58DD652C2E405E76007F49EC /* APIRequestManager.swift in Sources */,
				58DD652A2E405789007F49EC /* MessageRepositoryProtocol.swift in Sources */,
				58EDE2002E38A40F00AB1BA8 /* NetworkStatusManager.swift in Sources */,
				58EDE2012E38A40F00AB1BA8 /* ChatTarget.swift in Sources */,
				58EDE2022E38A40F00AB1BA8 /* VersionUpdateManager.swift in Sources */,
				58EDE2032E38A40F00AB1BA8 /* ChatListRepository.swift in Sources */,
				58EDE2042E38A40F00AB1BA8 /* APICommom.swift in Sources */,
				58EDE2052E38A40F00AB1BA8 /* CacheManager.swift in Sources */,
				58EDE2062E38A40F00AB1BA8 /* TypewriterEffectManager.swift in Sources */,
				58EDE2072E38A40F00AB1BA8 /* DatabaseVersion.swift in Sources */,
				58EDE2082E38A40F00AB1BA8 /* ChatRepositoryProtocol.swift in Sources */,
				58EDE2092E38A40F00AB1BA8 /* ConnectionManager.swift in Sources */,
				58EDE20A2E38A40F00AB1BA8 /* DatabaseIntegrityChecker.swift in Sources */,
				58EDE20B2E38A40F00AB1BA8 /* MessageLoader.swift in Sources */,
				58EDE20C2E38A40F00AB1BA8 /* UserSettings.swift in Sources */,
				58EDE20D2E38A40F00AB1BA8 /* MessageRepository.swift in Sources */,
				58EDE20E2E38A40F00AB1BA8 /* NetworkService.swift in Sources */,
				58EDE20F2E38A40F00AB1BA8 /* PricingTarget.swift in Sources */,
				58EDE2112E38A40F00AB1BA8 /* ChatRepository.swift in Sources */,
				58EDE2122E38A40F00AB1BA8 /* ChatSessionManager.swift in Sources */,
				58EDE2132E38A40F00AB1BA8 /* ConfigTarget.swift in Sources */,
				58DD65262E405289007F49EC /* OptimizedChatRowView.swift in Sources */,
				58EDE2142E38A40F00AB1BA8 /* AuthTarget.swift in Sources */,
				58EDE2152E38A40F00AB1BA8 /* BootsManager.swift in Sources */,
				58EDE2162E38A40F00AB1BA8 /* ChatListComponentFactory.swift in Sources */,
				58EDE2172E38A40F00AB1BA8 /* PurchaseTarget.swift in Sources */,
				58EDE2182E38A40F00AB1BA8 /* UnifiedSSEManager.swift in Sources */,
				58EDE2192E38A40F00AB1BA8 /* Preferences.swift in Sources */,
				58EDE21A2E38A40F00AB1BA8 /* AdvisorDatabaseManager.swift in Sources */,
				58EDE21B2E38A40F00AB1BA8 /* ShopManager.swift in Sources */,
				58EDE21C2E38A40F00AB1BA8 /* BaseMigration.swift in Sources */,
				58EDE21D2E38A40F00AB1BA8 /* ErrorRecoveryManager.swift in Sources */,
				588686582E162D0800A4C3FF /* EmailLoginView.swift in Sources */,
				588686592E162D0800A4C3FF /* OptimizedMessageView.swift in Sources */,
				5886865A2E162D0800A4C3FF /* MultiStepFormView.swift in Sources */,
				5886865B2E162D0800A4C3FF /* InputingAnimationView.swift in Sources */,
				5886865C2E162D0800A4C3FF /* EnhancedLoadingView.swift in Sources */,
				58DD65082E404CBE007F49EC /* StartupLoadingView.swift in Sources */,
				5886865D2E162D0800A4C3FF /* ChatConfigDatabaseManager.swift in Sources */,
				5886865E2E162D0800A4C3FF /* SideMenu.swift in Sources */,
				5886865F2E162D0800A4C3FF /* StepViewModel.swift in Sources */,
				588686602E162D0800A4C3FF /* ContentView.swift in Sources */,
				588686612E162D0800A4C3FF /* PreferLanguageView.swift in Sources */,
				588686622E162D0800A4C3FF /* RecognizeMessageView.swift in Sources */,
				588686632E162D0800A4C3FF /* InputBottomView.swift in Sources */,
				588686642E162D0800A4C3FF /* VerificationView.swift in Sources */,
				588686652E162D0800A4C3FF /* MessageBubblePreview.swift in Sources */,
				588686662E162D0800A4C3FF /* PurchaseView.swift in Sources */,
				588686672E162D0800A4C3FF /* SideMenuModifier.swift in Sources */,
				588686682E162D0800A4C3FF /* PricingView.swift in Sources */,
				588686692E162D0800A4C3FF /* ChatViewPreview.swift in Sources */,
				5886866A2E162D0800A4C3FF /* ArchivedChatsListView.swift in Sources */,
				5886866B2E162D0800A4C3FF /* StepProtocol.swift in Sources */,
				5886866C2E162D0800A4C3FF /* SettingsView.swift in Sources */,
				5886866D2E162D0800A4C3FF /* MessageBubble.swift in Sources */,
				5886866E2E162D0800A4C3FF /* InputField.swift in Sources */,
				5886866F2E162D0800A4C3FF /* MultiStepFormPreviewView.swift in Sources */,
				588686702E162D0800A4C3FF /* ChatView.swift in Sources */,
				588686712E162D0800A4C3FF /* StepPreviewView.swift in Sources */,
				9706CCAA2BC3F2730080FECE /* ChatAdvisorApp.swift in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin PBXTargetDependency section */
		9706CC782BC3CFA80080FECE /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = 9706CC652BC3CFA40080FECE /* ChatAdvisor */;
			targetProxy = 9706CC772BC3CFA80080FECE /* PBXContainerItemProxy */;
		};
		9706CC822BC3CFA90080FECE /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = 9706CC652BC3CFA40080FECE /* ChatAdvisor */;
			targetProxy = 9706CC812BC3CFA90080FECE /* PBXContainerItemProxy */;
		};
/* End PBXTargetDependency section */

/* Begin PBXVariantGroup section */
		6C17C4E12C09715B005BA6A3 /* Info.plist */ = {
			isa = PBXVariantGroup;
			children = (
				6C17C4E02C09715B005BA6A3 /* Base */,
				6CFCC22D2D65AB19004E0912 /* en */,
			);
			name = Info.plist;
			sourceTree = "<group>";
		};
		6CA36DE02C1833EB00AE82B2 /* InfoPlist.strings */ = {
			isa = PBXVariantGroup;
			children = (
				6CA36DE12C1833EB00AE82B2 /* en */,
				6CA36DE32C1833EF00AE82B2 /* ar */,
				6CA36DE42C1833F100AE82B2 /* zh-Hans */,
				6CA36DE52C1833F200AE82B2 /* ja */,
				6CA36DE82C1833F500AE82B2 /* ru */,
				6CA36DE92C1833F600AE82B2 /* th */,
				6CA36DEA2C1833F600AE82B2 /* tr */,
				6CA36DEB2C1833F700AE82B2 /* uk */,
				6CA36DEC2C1833F700AE82B2 /* vi */,
				6CA36E182C1D337F00AE82B2 /* ga */,
				6CA36E1B2C1D33A000AE82B2 /* zh-HK */,
				6CA36E1E2C1D33BC00AE82B2 /* zh-Hant */,
				6CA36E212C1D341900AE82B2 /* lo */,
				6CA36E242C1D359800AE82B2 /* ms */,
				6CA36E272C1D362100AE82B2 /* es */,
				6CA36E2A2C1D366000AE82B2 /* de */,
				6CA36E2D2C1D36A300AE82B2 /* ca */,
				6CA36E302C1D36ED00AE82B2 /* fr */,
				6CA36E332C1D372500AE82B2 /* it */,
				6CA36E362C1D38FC00AE82B2 /* ko */,
				6CA36E392C1D395300AE82B2 /* pl */,
				6CA36E3C2C1D3A1300AE82B2 /* es-419 */,
				6CA36E3F2C1D3A6000AE82B2 /* pt-BR */,
				6C6E228D2C3783930081A7D6 /* hr */,
				6C6E22902C37839D0081A7D6 /* cs */,
				6C6E22932C3783A00081A7D6 /* da */,
				6C6E22962C3783A50081A7D6 /* nl */,
				6C6E22992C3783AB0081A7D6 /* fi */,
				6C6E229C2C3783B10081A7D6 /* el */,
				6C6E229F2C3783B40081A7D6 /* he */,
				6C6E22A22C3783B70081A7D6 /* hi */,
				6C6E22A52C3783CE0081A7D6 /* hu */,
				6C6E22A82C3783D30081A7D6 /* id */,
				6C6E22AB2C3783D90081A7D6 /* nb */,
				6C6E22AE2C3783DE0081A7D6 /* ro */,
				6C6E22B12C3783E20081A7D6 /* sk */,
				6C6E22B42C3783E80081A7D6 /* sv */,
				6C6E22B72C3783F70081A7D6 /* ab */,
				6C62A6B02C461FDE002D40D5 /* en-AU */,
				6C62A6B32C462038002D40D5 /* en-IN */,
				6C62A6B62C46204C002D40D5 /* en-GB */,
				6C62A6B92C462051002D40D5 /* fr-CA */,
				6C62A6BC2C462055002D40D5 /* pt-PT */,
				6C03107E2CDC9AEF00E0C305 /* fil */,
				6C5F8B5C2CE1DE1300B2E3F9 /* fa */,
				6C5F8B5F2CE1DF1F00B2E3F9 /* ur-PK */,
				6C5F8B622CE1DFEA00B2E3F9 /* ps */,
				6C5F8B652CE1E0EE00B2E3F9 /* fr-CI */,
			);
			name = InfoPlist.strings;
			sourceTree = "<group>";
		};
		D6DC913D2C0C6C1F009DB0AB /* Localizable.strings */ = {
			isa = PBXVariantGroup;
			children = (
				D6DC913E2C0C6C1F009DB0AB /* en */,
				D6DC91402C0C6C28009DB0AB /* ar */,
				D6DC91412C0C6C2B009DB0AB /* zh-Hans */,
				D6DC91422C0C6C2C009DB0AB /* ja */,
				D6DC91442C0C6C2F009DB0AB /* ru */,
				D6DC91452C0C6C30009DB0AB /* th */,
				D6DC91462C0C6C31009DB0AB /* tr */,
				D6DC91472C0C6C32009DB0AB /* uk */,
				D6DC91482C0C6C32009DB0AB /* vi */,
				6CA36E172C1D337F00AE82B2 /* ga */,
				6CA36E1A2C1D33A000AE82B2 /* zh-HK */,
				6CA36E1D2C1D33BC00AE82B2 /* zh-Hant */,
				6CA36E202C1D341900AE82B2 /* lo */,
				6CA36E232C1D359800AE82B2 /* ms */,
				6CA36E262C1D362100AE82B2 /* es */,
				6CA36E292C1D366000AE82B2 /* de */,
				6CA36E2C2C1D36A300AE82B2 /* ca */,
				6CA36E2F2C1D36ED00AE82B2 /* fr */,
				6CA36E322C1D372500AE82B2 /* it */,
				6CA36E352C1D38FC00AE82B2 /* ko */,
				6CA36E382C1D395300AE82B2 /* pl */,
				6CA36E3B2C1D3A1300AE82B2 /* es-419 */,
				6CA36E3E2C1D3A6000AE82B2 /* pt-BR */,
				6C6E228C2C3783930081A7D6 /* hr */,
				6C6E228F2C37839D0081A7D6 /* cs */,
				6C6E22922C3783A00081A7D6 /* da */,
				6C6E22952C3783A50081A7D6 /* nl */,
				6C6E22982C3783AB0081A7D6 /* fi */,
				6C6E229B2C3783B00081A7D6 /* el */,
				6C6E229E2C3783B40081A7D6 /* he */,
				6C6E22A12C3783B70081A7D6 /* hi */,
				6C6E22A42C3783CE0081A7D6 /* hu */,
				6C6E22A72C3783D30081A7D6 /* id */,
				6C6E22AA2C3783D90081A7D6 /* nb */,
				6C6E22AD2C3783DE0081A7D6 /* ro */,
				6C6E22B02C3783E20081A7D6 /* sk */,
				6C6E22B32C3783E80081A7D6 /* sv */,
				6C6E22B62C3783F70081A7D6 /* ab */,
				6C62A6AF2C461FDE002D40D5 /* en-AU */,
				6C62A6B22C462038002D40D5 /* en-IN */,
				6C62A6B52C46204B002D40D5 /* en-GB */,
				6C62A6B82C462051002D40D5 /* fr-CA */,
				6C62A6BB2C462055002D40D5 /* pt-PT */,
				6C0310802CDC9B1300E0C305 /* fil */,
				6C5F8B5B2CE1DE1300B2E3F9 /* fa */,
				6C5F8B5E2CE1DF1F00B2E3F9 /* ur-PK */,
				6C5F8B612CE1DFEA00B2E3F9 /* ps */,
				6C5F8B642CE1E0EE00B2E3F9 /* fr-CI */,
			);
			name = Localizable.strings;
			sourceTree = "<group>";
		};
/* End PBXVariantGroup section */

/* Begin XCBuildConfiguration section */
		9706CC882BC3CFA90080FECE /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_LOCALIZABILITY_NONLOCALIZED = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = dwarf;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 17.4;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MTL_ENABLE_DEBUG_INFO = INCLUDE_SOURCE;
				MTL_FAST_MATH = YES;
				ONLY_ACTIVE_ARCH = YES;
				SDKROOT = iphoneos;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "DEBUG $(inherited)";
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
			};
			name = Debug;
		};
		9706CC892BC3CFA90080FECE /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_LOCALIZABILITY_NONLOCALIZED = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 17.4;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MTL_ENABLE_DEBUG_INFO = NO;
				MTL_FAST_MATH = YES;
				SDKROOT = iphoneos;
				SWIFT_COMPILATION_MODE = wholemodule;
				VALIDATE_PRODUCT = YES;
			};
			name = Release;
		};
		9706CC8B2BC3CFA90080FECE /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_ENTITLEMENTS = ChatAdvisor/ChatAdvisor.entitlements;
				CODE_SIGN_IDENTITY = "Apple Development";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_ASSET_PATHS = "\"ChatAdvisor/Preview Content\"";
				DEVELOPMENT_TEAM = 2XBCJAM843;
				ENABLE_PREVIEWS = YES;
				FRAMEWORK_SEARCH_PATHS = (
					"$(inherited)",
					"$(PROJECT_DIR)",
				);
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_FILE = ChatAdvisor/Info.plist;
				INFOPLIST_KEY_ITSAppUsesNonExemptEncryption = NO;
				INFOPLIST_KEY_NSLocalNetworkUsageDescription = 123;
				INFOPLIST_KEY_NSMicrophoneUsageDescription = "为了提供更优质的聊天体验，我们需要访问您的麦克风。通过启用麦克风权限，您将能够使用语音输入功能，与我们的应用进行更加便捷和高效的交流。";
				INFOPLIST_KEY_NSPhotoLibraryUsageDescription = "为了提供更优质的聊天体验，我们需要访问您的相册。通过启用相册权限，您将能够使用图片识别文本功能，与我们的应用进行更加便捷和高效的交流。";
				INFOPLIST_KEY_UIApplicationSceneManifest_Generation = YES;
				INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents = YES;
				INFOPLIST_KEY_UILaunchScreen_Generation = YES;
				INFOPLIST_KEY_UIRequiresFullScreen = YES;
				INFOPLIST_KEY_UISupportedInterfaceOrientations = "UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight UIInterfaceOrientationPortrait";
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPad = UIInterfaceOrientationPortrait;
				IPHONEOS_DEPLOYMENT_TARGET = 16.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				LOCALIZATION_EXPORT_SUPPORTED = YES;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				LOCALIZED_STRING_SWIFTUI_SUPPORT = YES;
				MARKETING_VERSION = 1.2.3;
				OTHER_LDFLAGS = "-ObjC";
				PRODUCT_BUNDLE_IDENTIFIER = com.sanva.chatadvisor;
				PRODUCT_NAME = "$(TARGET_NAME)";
				PROVISIONING_PROFILE_SPECIFIER = "";
				SUPPORTED_PLATFORMS = "iphoneos iphonesimulator xros xrsimulator";
				SUPPORTS_MACCATALYST = NO;
				SUPPORTS_MAC_DESIGNED_FOR_IPHONE_IPAD = NO;
				SUPPORTS_XR_DESIGNED_FOR_IPHONE_IPAD = NO;
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2,7";
			};
			name = Debug;
		};
		9706CC8C2BC3CFA90080FECE /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_ENTITLEMENTS = ChatAdvisor/ChatAdvisor.entitlements;
				CODE_SIGN_IDENTITY = "Apple Development";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_ASSET_PATHS = "\"ChatAdvisor/Preview Content\"";
				DEVELOPMENT_TEAM = 2XBCJAM843;
				ENABLE_PREVIEWS = YES;
				FRAMEWORK_SEARCH_PATHS = (
					"$(inherited)",
					"$(PROJECT_DIR)",
				);
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_FILE = ChatAdvisor/Info.plist;
				INFOPLIST_KEY_ITSAppUsesNonExemptEncryption = NO;
				INFOPLIST_KEY_NSLocalNetworkUsageDescription = 123;
				INFOPLIST_KEY_NSMicrophoneUsageDescription = "为了提供更优质的聊天体验，我们需要访问您的麦克风。通过启用麦克风权限，您将能够使用语音输入功能，与我们的应用进行更加便捷和高效的交流。";
				INFOPLIST_KEY_NSPhotoLibraryUsageDescription = "为了提供更优质的聊天体验，我们需要访问您的相册。通过启用相册权限，您将能够使用图片识别文本功能，与我们的应用进行更加便捷和高效的交流。";
				INFOPLIST_KEY_UIApplicationSceneManifest_Generation = YES;
				INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents = YES;
				INFOPLIST_KEY_UILaunchScreen_Generation = YES;
				INFOPLIST_KEY_UIRequiresFullScreen = YES;
				INFOPLIST_KEY_UISupportedInterfaceOrientations = "UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight UIInterfaceOrientationPortrait";
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPad = UIInterfaceOrientationPortrait;
				IPHONEOS_DEPLOYMENT_TARGET = 16.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				LOCALIZATION_EXPORT_SUPPORTED = YES;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				LOCALIZED_STRING_SWIFTUI_SUPPORT = YES;
				MARKETING_VERSION = 1.2.3;
				OTHER_LDFLAGS = "-ObjC";
				PRODUCT_BUNDLE_IDENTIFIER = com.sanva.chatadvisor;
				PRODUCT_NAME = "$(TARGET_NAME)";
				PROVISIONING_PROFILE_SPECIFIER = "";
				SUPPORTED_PLATFORMS = "iphoneos iphonesimulator xros xrsimulator";
				SUPPORTS_MACCATALYST = NO;
				SUPPORTS_MAC_DESIGNED_FOR_IPHONE_IPAD = NO;
				SUPPORTS_XR_DESIGNED_FOR_IPHONE_IPAD = NO;
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2,7";
			};
			name = Release;
		};
		9706CC8E2BC3CFA90080FECE /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_EMBED_SWIFT_STANDARD_LIBRARIES = YES;
				BUNDLE_LOADER = "$(TEST_HOST)";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = 2XBCJAM843;
				GENERATE_INFOPLIST_FILE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 17.4;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.sanva.JunShiTests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/ChatAdvisor.app/$(BUNDLE_EXECUTABLE_FOLDER_PATH)/ChatAdvisor";
			};
			name = Debug;
		};
		9706CC8F2BC3CFA90080FECE /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_EMBED_SWIFT_STANDARD_LIBRARIES = YES;
				BUNDLE_LOADER = "$(TEST_HOST)";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = 2XBCJAM843;
				GENERATE_INFOPLIST_FILE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 17.4;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.sanva.JunShiTests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/ChatAdvisor.app/$(BUNDLE_EXECUTABLE_FOLDER_PATH)/ChatAdvisor";
			};
			name = Release;
		};
		9706CC912BC3CFA90080FECE /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_EMBED_SWIFT_STANDARD_LIBRARIES = YES;
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = 2XBCJAM843;
				GENERATE_INFOPLIST_FILE = YES;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.sanva.JunShiUITests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				TEST_TARGET_NAME = JunShi;
			};
			name = Debug;
		};
		9706CC922BC3CFA90080FECE /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_EMBED_SWIFT_STANDARD_LIBRARIES = YES;
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = 2XBCJAM843;
				GENERATE_INFOPLIST_FILE = YES;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.sanva.JunShiUITests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				TEST_TARGET_NAME = JunShi;
			};
			name = Release;
		};
		9706CCB52BC3F2730080FECE /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_ASSET_PATHS = "\"JunShi/Preview Content\"";
				DEVELOPMENT_TEAM = 2XBCJAM843;
				ENABLE_PREVIEWS = YES;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_KEY_UIApplicationSceneManifest_Generation = YES;
				INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents = YES;
				INFOPLIST_KEY_UILaunchScreen_Generation = YES;
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPad = "UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPhone = "UIInterfaceOrientationPortrait UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				IPHONEOS_DEPLOYMENT_TARGET = 15.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.sanva.debug.JunShi;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Debug;
		};
		9706CCB62BC3F2730080FECE /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_ASSET_PATHS = "\"JunShi/Preview Content\"";
				DEVELOPMENT_TEAM = 2XBCJAM843;
				ENABLE_PREVIEWS = YES;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_KEY_UIApplicationSceneManifest_Generation = YES;
				INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents = YES;
				INFOPLIST_KEY_UILaunchScreen_Generation = YES;
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPad = "UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPhone = "UIInterfaceOrientationPortrait UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				IPHONEOS_DEPLOYMENT_TARGET = 15.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.sanva.debug.JunShi;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		9706CC612BC3CFA40080FECE /* Build configuration list for PBXProject "ChatAdvisor" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				9706CC882BC3CFA90080FECE /* Debug */,
				9706CC892BC3CFA90080FECE /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		9706CC8A2BC3CFA90080FECE /* Build configuration list for PBXNativeTarget "ChatAdvisor" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				9706CC8B2BC3CFA90080FECE /* Debug */,
				9706CC8C2BC3CFA90080FECE /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		9706CC8D2BC3CFA90080FECE /* Build configuration list for PBXNativeTarget "ChatAdvisorTests" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				9706CC8E2BC3CFA90080FECE /* Debug */,
				9706CC8F2BC3CFA90080FECE /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		9706CC902BC3CFA90080FECE /* Build configuration list for PBXNativeTarget "ChatAdvisorUITests" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				9706CC912BC3CFA90080FECE /* Debug */,
				9706CC922BC3CFA90080FECE /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		9706CCB42BC3F2730080FECE /* Build configuration list for PBXNativeTarget "ChatAdvisor Debug" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				9706CCB52BC3F2730080FECE /* Debug */,
				9706CCB62BC3F2730080FECE /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */

/* Begin XCRemoteSwiftPackageReference section */
		6C3987362BEB26CF0026E28C /* XCRemoteSwiftPackageReference "swift-eventsource" */ = {
			isa = XCRemoteSwiftPackageReference;
			repositoryURL = "https://github.com/LaunchDarkly/swift-eventsource.git";
			requirement = {
				kind = upToNextMajorVersion;
				minimumVersion = 3.2.0;
			};
		};
		6C4529CF2C52262100489F07 /* XCRemoteSwiftPackageReference "facebook-ios-sdk" */ = {
			isa = XCRemoteSwiftPackageReference;
			repositoryURL = "https://github.com/facebook/facebook-ios-sdk";
			requirement = {
				kind = upToNextMajorVersion;
				minimumVersion = 16.1.0;
			};
		};
		6C62A6C32C48C7AC002D40D5 /* XCRemoteSwiftPackageReference "EventSource" */ = {
			isa = XCRemoteSwiftPackageReference;
			repositoryURL = "https://github.com/Recouse/EventSource.git";
			requirement = {
				kind = upToNextMajorVersion;
				minimumVersion = 0.0.7;
			};
		};
		6C71C88B2C3CCD020001B917 /* XCRemoteSwiftPackageReference "StepperView" */ = {
			isa = XCRemoteSwiftPackageReference;
			repositoryURL = "https://github.com/badrinathvm/StepperView.git";
			requirement = {
				kind = upToNextMajorVersion;
				minimumVersion = 1.6.7;
			};
		};
		6C90D6942CE763A600AD016E /* XCRemoteSwiftPackageReference "tiktok-opensdk-ios" */ = {
			isa = XCRemoteSwiftPackageReference;
			repositoryURL = "https://github.com/tiktok/tiktok-opensdk-ios.git";
			requirement = {
				branch = main;
				kind = branch;
			};
		};
		6C97395F2BDE2DFB00F53F1D /* XCRemoteSwiftPackageReference "LookinServer" */ = {
			isa = XCRemoteSwiftPackageReference;
			repositoryURL = "https://github.com/QMUI/LookinServer/";
			requirement = {
				kind = upToNextMajorVersion;
				minimumVersion = 1.2.8;
			};
		};
		6CA0CEA92BD24C1C00A6B32B /* XCRemoteSwiftPackageReference "SwifterSwift" */ = {
			isa = XCRemoteSwiftPackageReference;
			repositoryURL = "https://github.com/SwifterSwift/SwifterSwift.git";
			requirement = {
				kind = upToNextMajorVersion;
				minimumVersion = 6.2.0;
			};
		};
		6CC844262BD2096A00B4698C /* XCRemoteSwiftPackageReference "keychain-swift" */ = {
			isa = XCRemoteSwiftPackageReference;
			repositoryURL = "https://github.com/evgenyneu/keychain-swift.git";
			requirement = {
				kind = upToNextMajorVersion;
				minimumVersion = 22.0.0;
			};
		};
		6CC844312BD21B8800B4698C /* XCRemoteSwiftPackageReference "Localize-Swift" */ = {
			isa = XCRemoteSwiftPackageReference;
			repositoryURL = "https://github.com/marmelroy/Localize-Swift.git";
			requirement = {
				kind = upToNextMajorVersion;
				minimumVersion = 3.2.0;
			};
		};
		6CD6752F2BF5EB58008134B8 /* XCRemoteSwiftPackageReference "Splash" */ = {
			isa = XCRemoteSwiftPackageReference;
			repositoryURL = "https://github.com/JohnSundell/Splash";
			requirement = {
				kind = upToNextMajorVersion;
				minimumVersion = 0.16.0;
			};
		};
		6CDA93F62BF483EA00C52DDF /* XCRemoteSwiftPackageReference "swift-markdown-ui" */ = {
			isa = XCRemoteSwiftPackageReference;
			repositoryURL = "https://github.com/gonzalezreal/swift-markdown-ui";
			requirement = {
				kind = upToNextMajorVersion;
				minimumVersion = 2.3.0;
			};
		};
		6CE61D662BEF02D400B8B4F1 /* XCRemoteSwiftPackageReference "SimpleToast" */ = {
			isa = XCRemoteSwiftPackageReference;
			repositoryURL = "https://github.com/sanzaru/SimpleToast.git";
			requirement = {
				kind = upToNextMajorVersion;
				minimumVersion = 0.8.1;
			};
		};
		6CFEAFFE2BE0DFD70046C53A /* XCRemoteSwiftPackageReference "wcdb" */ = {
			isa = XCRemoteSwiftPackageReference;
			repositoryURL = "https://github.com/Tencent/wcdb.git";
			requirement = {
				kind = exactVersion;
				version = 2.1.10;
			};
		};
		9706CC9C2BC3ECB50080FECE /* XCRemoteSwiftPackageReference "firebase-ios-sdk" */ = {
			isa = XCRemoteSwiftPackageReference;
			repositoryURL = "https://github.com/firebase/firebase-ios-sdk.git";
			requirement = {
				kind = upToNextMajorVersion;
				minimumVersion = 11.2.0;
			};
		};
		9706CCA72BC3F2730080FECE /* XCRemoteSwiftPackageReference "firebase-ios-sdk" */ = {
			isa = XCRemoteSwiftPackageReference;
			repositoryURL = "https://github.com/firebase/firebase-ios-sdk.git";
			requirement = {
				kind = upToNextMajorVersion;
				minimumVersion = 10.23.1;
			};
		};
		9777D1032BC7DAEE00619386 /* XCRemoteSwiftPackageReference "Moya" */ = {
			isa = XCRemoteSwiftPackageReference;
			repositoryURL = "https://github.com/Moya/Moya.git";
			requirement = {
				kind = upToNextMajorVersion;
				minimumVersion = 15.0.3;
			};
		};
		9777D1062BC7DB3200619386 /* XCRemoteSwiftPackageReference "Alamofire" */ = {
			isa = XCRemoteSwiftPackageReference;
			repositoryURL = "https://github.com/Alamofire/Alamofire.git";
			requirement = {
				kind = upToNextMajorVersion;
				minimumVersion = 5.9.1;
			};
		};
		D66B674D2C2B03EB00DB7598 /* XCRemoteSwiftPackageReference "GoogleSignIn-iOS" */ = {
			isa = XCRemoteSwiftPackageReference;
			repositoryURL = "https://github.com/google/GoogleSignIn-iOS";
			requirement = {
				kind = upToNextMajorVersion;
				minimumVersion = 8.0.0;
			};
		};
/* End XCRemoteSwiftPackageReference section */

/* Begin XCSwiftPackageProductDependency section */
		6C3987372BEB26CF0026E28C /* LDSwiftEventSource */ = {
			isa = XCSwiftPackageProductDependency;
			package = 6C3987362BEB26CF0026E28C /* XCRemoteSwiftPackageReference "swift-eventsource" */;
			productName = LDSwiftEventSource;
		};
		6C4529D02C52262100489F07 /* FacebookCore */ = {
			isa = XCSwiftPackageProductDependency;
			package = 6C4529CF2C52262100489F07 /* XCRemoteSwiftPackageReference "facebook-ios-sdk" */;
			productName = FacebookCore;
		};
		6C4529D22C52262100489F07 /* FacebookLogin */ = {
			isa = XCSwiftPackageProductDependency;
			package = 6C4529CF2C52262100489F07 /* XCRemoteSwiftPackageReference "facebook-ios-sdk" */;
			productName = FacebookLogin;
		};
		6C62A6C42C48C7AC002D40D5 /* EventSource */ = {
			isa = XCSwiftPackageProductDependency;
			package = 6C62A6C32C48C7AC002D40D5 /* XCRemoteSwiftPackageReference "EventSource" */;
			productName = EventSource;
		};
		6C6E214C2C33B2170081A7D6 /* FirebaseMessaging */ = {
			isa = XCSwiftPackageProductDependency;
			package = 9706CC9C2BC3ECB50080FECE /* XCRemoteSwiftPackageReference "firebase-ios-sdk" */;
			productName = FirebaseMessaging;
		};
		6C71C88C2C3CCD020001B917 /* StepperView */ = {
			isa = XCSwiftPackageProductDependency;
			package = 6C71C88B2C3CCD020001B917 /* XCRemoteSwiftPackageReference "StepperView" */;
			productName = StepperView;
		};
		6C90D6952CE763A600AD016E /* TikTokOpenSDKCore */ = {
			isa = XCSwiftPackageProductDependency;
			package = 6C90D6942CE763A600AD016E /* XCRemoteSwiftPackageReference "tiktok-opensdk-ios" */;
			productName = TikTokOpenSDKCore;
		};
		6C90D6972CE7645300AD016E /* TikTokOpenAuthSDK */ = {
			isa = XCSwiftPackageProductDependency;
			package = 6C90D6942CE763A600AD016E /* XCRemoteSwiftPackageReference "tiktok-opensdk-ios" */;
			productName = TikTokOpenAuthSDK;
		};
		6C9739602BDE2DFB00F53F1D /* LookinServer */ = {
			isa = XCSwiftPackageProductDependency;
			package = 6C97395F2BDE2DFB00F53F1D /* XCRemoteSwiftPackageReference "LookinServer" */;
			productName = LookinServer;
		};
		6CA0CEAA2BD24C1C00A6B32B /* SwifterSwift */ = {
			isa = XCSwiftPackageProductDependency;
			package = 6CA0CEA92BD24C1C00A6B32B /* XCRemoteSwiftPackageReference "SwifterSwift" */;
			productName = SwifterSwift;
		};
		6CC844272BD2096A00B4698C /* KeychainSwift */ = {
			isa = XCSwiftPackageProductDependency;
			package = 6CC844262BD2096A00B4698C /* XCRemoteSwiftPackageReference "keychain-swift" */;
			productName = KeychainSwift;
		};
		6CC844322BD21B8800B4698C /* Localize_Swift */ = {
			isa = XCSwiftPackageProductDependency;
			package = 6CC844312BD21B8800B4698C /* XCRemoteSwiftPackageReference "Localize-Swift" */;
			productName = Localize_Swift;
		};
		6CD675302BF5EB58008134B8 /* Splash */ = {
			isa = XCSwiftPackageProductDependency;
			package = 6CD6752F2BF5EB58008134B8 /* XCRemoteSwiftPackageReference "Splash" */;
			productName = Splash;
		};
		6CDA93F72BF483EA00C52DDF /* MarkdownUI */ = {
			isa = XCSwiftPackageProductDependency;
			package = 6CDA93F62BF483EA00C52DDF /* XCRemoteSwiftPackageReference "swift-markdown-ui" */;
			productName = MarkdownUI;
		};
		6CE61D672BEF02D400B8B4F1 /* SimpleToast */ = {
			isa = XCSwiftPackageProductDependency;
			package = 6CE61D662BEF02D400B8B4F1 /* XCRemoteSwiftPackageReference "SimpleToast" */;
			productName = SimpleToast;
		};
		6CFEAFFF2BE0DFD70046C53A /* WCDBSwift */ = {
			isa = XCSwiftPackageProductDependency;
			package = 6CFEAFFE2BE0DFD70046C53A /* XCRemoteSwiftPackageReference "wcdb" */;
			productName = WCDBSwift;
		};
		9706CC9D2BC3ECB50080FECE /* FirebaseAnalytics */ = {
			isa = XCSwiftPackageProductDependency;
			package = 9706CC9C2BC3ECB50080FECE /* XCRemoteSwiftPackageReference "firebase-ios-sdk" */;
			productName = FirebaseAnalytics;
		};
		9706CCA42BC3F2730080FECE /* JunShiPackage */ = {
			isa = XCSwiftPackageProductDependency;
			productName = JunShiPackage;
		};
		9706CCA52BC3F2730080FECE /* JunShiPackage */ = {
			isa = XCSwiftPackageProductDependency;
			productName = JunShiPackage;
		};
		9706CCA62BC3F2730080FECE /* FirebaseAnalytics */ = {
			isa = XCSwiftPackageProductDependency;
			package = 9706CCA72BC3F2730080FECE /* XCRemoteSwiftPackageReference "firebase-ios-sdk" */;
			productName = FirebaseAnalytics;
		};
		9777D1042BC7DAEE00619386 /* Moya */ = {
			isa = XCSwiftPackageProductDependency;
			package = 9777D1032BC7DAEE00619386 /* XCRemoteSwiftPackageReference "Moya" */;
			productName = Moya;
		};
		9777D1072BC7DB3200619386 /* Alamofire */ = {
			isa = XCSwiftPackageProductDependency;
			package = 9777D1062BC7DB3200619386 /* XCRemoteSwiftPackageReference "Alamofire" */;
			productName = Alamofire;
		};
		D66B674E2C2B03EB00DB7598 /* GoogleSignInSwift */ = {
			isa = XCSwiftPackageProductDependency;
			package = D66B674D2C2B03EB00DB7598 /* XCRemoteSwiftPackageReference "GoogleSignIn-iOS" */;
			productName = GoogleSignInSwift;
		};
		D6C84FE52BC436D0005FBCD6 /* FirebaseCrashlytics */ = {
			isa = XCSwiftPackageProductDependency;
			package = 9706CC9C2BC3ECB50080FECE /* XCRemoteSwiftPackageReference "firebase-ios-sdk" */;
			productName = FirebaseCrashlytics;
		};
/* End XCSwiftPackageProductDependency section */
	};
	rootObject = 9706CC5E2BC3CFA40080FECE /* Project object */;
}
