#!/bin/bash

# ChatAdvisor PM2 便利部署脚本
# 支持前端和后端的独立部署管理

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m'

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_header() {
    echo -e "${CYAN}[HEADER]${NC} $1"
}

# 显示帮助信息
show_help() {
    echo -e "${CYAN}ChatAdvisor PM2 便利部署脚本${NC}"
    echo ""
    echo "用法: $0 [组件] [操作] [环境]"
    echo ""
    echo "组件:"
    echo "  frontend    前端服务 (admin-frontend)"
    echo "  backend     后端服务 (ChatAdvisorServer)"
    echo "  all         所有服务"
    echo ""
    echo "操作:"
    echo "  deploy      部署服务 (构建+启动)"
    echo "  start       启动服务"
    echo "  stop        停止服务"
    echo "  restart     重启服务"
    echo "  status      查看状态"
    echo "  logs        查看日志"
    echo "  build       仅构建"
    echo "  health      健康检查"
    echo ""
    echo "环境:"
    echo "  production  生产环境 (默认)"
    echo "  development 开发环境"
    echo ""
    echo "示例:"
    echo "  $0 frontend deploy              # 部署前端到生产环境"
    echo "  $0 backend start development    # 启动后端开发环境"
    echo "  $0 all status                   # 查看所有服务状态"
    echo "  $0 frontend logs production     # 查看前端生产环境日志"
    echo ""
    echo "快捷命令:"
    echo "  $0 --help                       # 显示帮助"
    echo "  $0 --version                    # 显示版本"
    echo "  $0 --list                       # 列出所有服务"
}

# 显示版本信息
show_version() {
    echo "ChatAdvisor PM2 便利部署脚本 v1.0.0"
    echo "支持前端和后端的独立部署管理"
}

# 列出所有服务
list_services() {
    log_header "ChatAdvisor 服务列表"
    echo ""
    echo "前端服务:"
    echo "  - admin-frontend-release (生产环境, 端口: 34001)"
    echo "  - admin-frontend-debug   (开发环境, 端口: 54001)"
    echo ""
    echo "后端服务:"
    echo "  - chat-advisor-release   (生产环境, 端口: 53001)"
    echo "  - chat-advisor-debug     (开发环境, 端口: 33001)"
    echo ""
    echo "当前PM2状态:"
    pm2 status 2>/dev/null || echo "PM2未运行或无服务"
}

# 解析参数
parse_args() {
    COMPONENT=""
    ACTION=""
    ENVIRONMENT="production"
    
    # 处理特殊参数
    case "$1" in
        --help|-h)
            show_help
            exit 0
            ;;
        --version|-v)
            show_version
            exit 0
            ;;
        --list|-l)
            list_services
            exit 0
            ;;
    esac
    
    # 解析组件
    case "$1" in
        frontend|front|f)
            COMPONENT="frontend"
            ;;
        backend|back|b)
            COMPONENT="backend"
            ;;
        all|a)
            COMPONENT="all"
            ;;
        *)
            log_error "未知组件: $1"
            echo ""
            show_help
            exit 1
            ;;
    esac
    
    # 解析操作
    case "$2" in
        deploy|d)
            ACTION="deploy"
            ;;
        start|s)
            ACTION="start"
            ;;
        stop)
            ACTION="stop"
            ;;
        restart|r)
            ACTION="restart"
            ;;
        status|st)
            ACTION="status"
            ;;
        logs|log|l)
            ACTION="logs"
            ;;
        build|b)
            ACTION="build"
            ;;
        health|h)
            ACTION="health"
            ;;
        *)
            log_error "未知操作: $2"
            echo ""
            show_help
            exit 1
            ;;
    esac
    
    # 解析环境
    if [[ -n "$3" ]]; then
        case "$3" in
            production|prod|p)
                ENVIRONMENT="production"
                ;;
            development|dev|d)
                ENVIRONMENT="development"
                ;;
            *)
                log_error "未知环境: $3"
                echo ""
                show_help
                exit 1
                ;;
        esac
    fi
}

# 获取服务名称
get_service_name() {
    local component="$1"
    local env="$2"
    
    case "$component" in
        frontend)
            if [[ "$env" == "production" ]]; then
                echo "admin-frontend-release"
            else
                echo "admin-frontend-debug"
            fi
            ;;
        backend)
            if [[ "$env" == "production" ]]; then
                echo "chat-advisor-release"
            else
                echo "chat-advisor-debug"
            fi
            ;;
    esac
}

# 获取服务端口
get_service_port() {
    local component="$1"
    local env="$2"
    
    case "$component" in
        frontend)
            if [[ "$env" == "production" ]]; then
                echo "34001"
            else
                echo "54001"
            fi
            ;;
        backend)
            if [[ "$env" == "production" ]]; then
                echo "53001"
            else
                echo "33001"
            fi
            ;;
    esac
}

# 部署前端
deploy_frontend() {
    local env="$1"
    
    log_header "部署前端服务 ($env)"
    
    cd admin-frontend
    
    if [[ -f "scripts/deploy.sh" ]]; then
        ./scripts/deploy.sh --env "$env"
    else
        log_warning "未找到前端部署脚本，使用默认流程"
        
        # 安装依赖
        npm install
        
        # 构建项目
        if [[ "$env" == "production" ]]; then
            npm run build:prod
        else
            npm run build
        fi
        
        # 启动服务
        local service_name=$(get_service_name "frontend" "$env")
        pm2 stop "$service_name" 2>/dev/null || true
        pm2 start pm2.config.js --only "$service_name"
    fi
    
    cd ..
}

# 部署后端
deploy_backend() {
    local env="$1"
    
    log_header "部署后端服务 ($env)"
    
    cd ChatAdvisorServer
    
    if [[ -f "scripts/deploy-backend.sh" ]]; then
        ./scripts/deploy-backend.sh --env "$env"
    else
        log_warning "未找到后端部署脚本，使用默认流程"
        
        # 安装依赖
        if [[ "$env" == "production" ]]; then
            npm install --production
        else
            npm install
        fi
        
        # 构建项目
        npm run build
        
        # 启动服务
        local service_name=$(get_service_name "backend" "$env")
        pm2 stop "$service_name" 2>/dev/null || true
        pm2 start pm2.config.js --only "$service_name"
    fi
    
    cd ..
}

# 执行操作
execute_action() {
    local component="$1"
    local action="$2"
    local env="$3"
    
    case "$action" in
        deploy)
            case "$component" in
                frontend)
                    deploy_frontend "$env"
                    ;;
                backend)
                    deploy_backend "$env"
                    ;;
                all)
                    deploy_frontend "$env"
                    deploy_backend "$env"
                    ;;
            esac
            ;;
        start|stop|restart)
            case "$component" in
                frontend)
                    local service_name=$(get_service_name "frontend" "$env")
                    pm2 "$action" "$service_name"
                    ;;
                backend)
                    local service_name=$(get_service_name "backend" "$env")
                    pm2 "$action" "$service_name"
                    ;;
                all)
                    local frontend_service=$(get_service_name "frontend" "$env")
                    local backend_service=$(get_service_name "backend" "$env")
                    pm2 "$action" "$frontend_service" "$backend_service"
                    ;;
            esac
            ;;
        status)
            if [[ "$component" == "all" ]]; then
                pm2 status
            else
                local service_name=$(get_service_name "$component" "$env")
                pm2 show "$service_name"
            fi
            ;;
        logs)
            case "$component" in
                frontend|backend)
                    local service_name=$(get_service_name "$component" "$env")
                    pm2 logs "$service_name"
                    ;;
                all)
                    pm2 logs
                    ;;
            esac
            ;;
        build)
            case "$component" in
                frontend)
                    cd admin-frontend
                    if [[ "$env" == "production" ]]; then
                        npm run build:prod
                    else
                        npm run build
                    fi
                    cd ..
                    ;;
                backend)
                    cd ChatAdvisorServer
                    npm run build
                    cd ..
                    ;;
                all)
                    cd admin-frontend && npm run build:prod && cd ..
                    cd ChatAdvisorServer && npm run build && cd ..
                    ;;
            esac
            ;;
        health)
            case "$component" in
                frontend)
                    local port=$(get_service_port "frontend" "$env")
                    curl -f "http://localhost:$port" > /dev/null && log_success "前端服务健康" || log_error "前端服务异常"
                    ;;
                backend)
                    local port=$(get_service_port "backend" "$env")
                    curl -f "http://localhost:$port/health" > /dev/null && log_success "后端服务健康" || log_error "后端服务异常"
                    ;;
                all)
                    local frontend_port=$(get_service_port "frontend" "$env")
                    local backend_port=$(get_service_port "backend" "$env")
                    
                    curl -f "http://localhost:$frontend_port" > /dev/null && log_success "前端服务健康" || log_error "前端服务异常"
                    curl -f "http://localhost:$backend_port/health" > /dev/null && log_success "后端服务健康" || log_error "后端服务异常"
                    ;;
            esac
            ;;
    esac
}

# 显示操作结果
show_result() {
    local component="$1"
    local action="$2"
    local env="$3"
    
    echo ""
    log_success "操作完成: $component $action ($env)"
    
    if [[ "$action" == "deploy" || "$action" == "start" || "$action" == "restart" ]]; then
        echo ""
        echo "服务访问地址:"
        
        case "$component" in
            frontend)
                local port=$(get_service_port "frontend" "$env")
                echo "  - 前端: http://localhost:$port"
                ;;
            backend)
                local port=$(get_service_port "backend" "$env")
                echo "  - 后端: http://localhost:$port"
                ;;
            all)
                local frontend_port=$(get_service_port "frontend" "$env")
                local backend_port=$(get_service_port "backend" "$env")
                echo "  - 前端: http://localhost:$frontend_port"
                echo "  - 后端: http://localhost:$backend_port"
                ;;
        esac
        
        echo ""
        echo "PM2管理命令:"
        echo "  - 查看状态: pm2 status"
        echo "  - 查看日志: pm2 logs"
        echo "  - 监控服务: pm2 monit"
    fi
}

# 主函数
main() {
    # 检查参数数量
    if [[ $# -lt 2 ]]; then
        log_error "参数不足"
        echo ""
        show_help
        exit 1
    fi
    
    # 解析参数
    parse_args "$@"
    
    # 检查PM2
    if ! command -v pm2 &> /dev/null; then
        log_error "PM2 未安装，请先安装PM2"
        exit 1
    fi
    
    # 执行操作
    log_info "执行操作: $COMPONENT $ACTION ($ENVIRONMENT)"
    execute_action "$COMPONENT" "$ACTION" "$ENVIRONMENT"
    
    # 显示结果
    show_result "$COMPONENT" "$ACTION" "$ENVIRONMENT"
}

# 运行主函数
main "$@"
