// ChatAdvisor GitLab 流水线验证测试
// 验证GitLab流水线页面和部署结果

const { test, expect } = require('@playwright/test');
const axios = require('axios');

// 测试配置
const GITLAB_URL = 'https://gitlab.zweiteng.tk/server/admin-frontend/-/pipelines';
const DEPLOY_HOST = process.env.DEPLOY_HOST || '**************';
const FRONTEND_PORT = process.env.FRONTEND_PORT || '34001';
const BACKEND_PORT = process.env.BACKEND_PORT || '53001';

test.describe('GitLab 流水线验证', () => {
  
  test('访问GitLab流水线页面', async ({ page }) => {
    console.log('🔍 测试GitLab流水线页面访问...');
    
    // 访问GitLab流水线页面
    await page.goto(GITLAB_URL);
    
    // 等待页面加载
    await page.waitForLoadState('networkidle');
    
    // 验证页面标题
    await expect(page).toHaveTitle(/Pipelines|GitLab/);
    
    // 验证页面包含流水线相关内容
    const pageContent = await page.textContent('body');
    expect(pageContent).toContain('Pipeline');
    
    console.log('✅ GitLab流水线页面访问成功');
  });
  
  test('检查最新流水线状态', async ({ page }) => {
    console.log('🔍 检查最新流水线状态...');
    
    await page.goto(GITLAB_URL);
    await page.waitForLoadState('networkidle');
    
    // 查找流水线状态元素
    const pipelineElements = await page.locator('[data-testid="pipeline-url-link"], .pipeline-status, .ci-status').all();
    
    if (pipelineElements.length > 0) {
      console.log(`📊 找到 ${pipelineElements.length} 个流水线元素`);
      
      // 检查第一个流水线的状态
      const firstPipeline = pipelineElements[0];
      const pipelineText = await firstPipeline.textContent();
      
      console.log(`📋 最新流水线状态: ${pipelineText}`);
      
      // 验证流水线状态不是失败状态
      expect(pipelineText.toLowerCase()).not.toContain('failed');
      expect(pipelineText.toLowerCase()).not.toContain('error');
      
    } else {
      console.log('⚠️ 未找到流水线状态元素');
    }
    
    console.log('✅ 流水线状态检查完成');
  });
  
});

test.describe('部署结果验证', () => {
  
  test('验证前端服务可访问性', async ({ page }) => {
    console.log('🔍 测试前端服务可访问性...');
    
    const frontendUrl = `http://${DEPLOY_HOST}:${FRONTEND_PORT}`;
    console.log(`📡 访问前端URL: ${frontendUrl}`);
    
    // 访问前端服务
    const response = await page.goto(frontendUrl);
    
    // 验证响应状态
    expect(response.status()).toBe(200);
    
    // 等待页面加载
    await page.waitForLoadState('networkidle');
    
    // 验证页面内容
    const title = await page.title();
    console.log(`📄 页面标题: ${title}`);
    
    // 验证页面包含预期内容
    const bodyContent = await page.textContent('body');
    expect(bodyContent.length).toBeGreaterThan(0);
    
    // 检查是否有错误信息
    const errorElements = await page.locator('text=/error|错误|失败/i').count();
    expect(errorElements).toBe(0);
    
    console.log('✅ 前端服务验证通过');
  });
  
  test('验证前端页面功能', async ({ page }) => {
    console.log('🔍 测试前端页面基本功能...');
    
    const frontendUrl = `http://${DEPLOY_HOST}:${FRONTEND_PORT}`;
    await page.goto(frontendUrl);
    await page.waitForLoadState('networkidle');
    
    // 检查页面是否包含导航元素
    const navElements = await page.locator('nav, .nav, .navigation, .menu').count();
    if (navElements > 0) {
      console.log('📱 发现导航元素');
    }
    
    // 检查页面是否包含主要内容区域
    const contentElements = await page.locator('main, .main, .content, #app, #root').count();
    expect(contentElements).toBeGreaterThan(0);
    
    // 检查页面是否正确加载CSS
    const stylesheets = await page.locator('link[rel="stylesheet"]').count();
    console.log(`🎨 加载的样式表数量: ${stylesheets}`);
    
    // 检查页面是否正确加载JavaScript
    const scripts = await page.locator('script[src]').count();
    console.log(`📜 加载的脚本数量: ${scripts}`);
    
    console.log('✅ 前端页面功能验证通过');
  });
  
  test('验证后端API可访问性', async ({ request }) => {
    console.log('🔍 测试后端API可访问性...');
    
    const backendUrl = `http://${DEPLOY_HOST}:${BACKEND_PORT}`;
    console.log(`📡 访问后端URL: ${backendUrl}`);
    
    // 测试健康检查端点
    const healthUrl = `${backendUrl}/health`;
    const healthResponse = await request.get(healthUrl);
    
    expect(healthResponse.status()).toBe(200);
    
    const healthData = await healthResponse.text();
    console.log(`💓 健康检查响应: ${healthData}`);
    
    // 如果是JSON响应，验证格式
    try {
      const healthJson = JSON.parse(healthData);
      expect(healthJson).toBeDefined();
      console.log('📋 后端返回有效JSON格式');
    } catch (e) {
      console.log('📋 后端返回非JSON格式');
    }
    
    console.log('✅ 后端API验证通过');
  });
  
  test('验证前后端通信', async ({ page }) => {
    console.log('🔍 测试前后端通信...');
    
    const frontendUrl = `http://${DEPLOY_HOST}:${FRONTEND_PORT}`;
    
    // 监听网络请求
    const apiRequests = [];
    page.on('request', request => {
      if (request.url().includes(DEPLOY_HOST) && request.url().includes(BACKEND_PORT)) {
        apiRequests.push(request.url());
      }
    });
    
    // 访问前端页面
    await page.goto(frontendUrl);
    await page.waitForLoadState('networkidle');
    
    // 等待可能的API调用
    await page.waitForTimeout(3000);
    
    console.log(`📡 检测到 ${apiRequests.length} 个API请求`);
    
    if (apiRequests.length > 0) {
      console.log('🔗 前后端通信正常');
      apiRequests.forEach((url, index) => {
        console.log(`   ${index + 1}. ${url}`);
      });
    } else {
      console.log('ℹ️ 未检测到API请求（可能是静态页面）');
    }
    
    console.log('✅ 前后端通信验证完成');
  });
  
});

test.describe('性能和稳定性测试', () => {
  
  test('页面加载性能测试', async ({ page }) => {
    console.log('🔍 测试页面加载性能...');
    
    const frontendUrl = `http://${DEPLOY_HOST}:${FRONTEND_PORT}`;
    
    // 记录开始时间
    const startTime = Date.now();
    
    // 访问页面
    await page.goto(frontendUrl);
    await page.waitForLoadState('networkidle');
    
    // 计算加载时间
    const loadTime = Date.now() - startTime;
    console.log(`⏱️ 页面加载时间: ${loadTime}ms`);
    
    // 验证加载时间在合理范围内（10秒内）
    expect(loadTime).toBeLessThan(10000);
    
    // 如果加载时间过长，给出警告
    if (loadTime > 5000) {
      console.log('⚠️ 页面加载时间较长，建议优化');
    } else {
      console.log('✅ 页面加载性能良好');
    }
  });
  
  test('服务稳定性测试', async ({ request }) => {
    console.log('🔍 测试服务稳定性...');
    
    const frontendUrl = `http://${DEPLOY_HOST}:${FRONTEND_PORT}`;
    const backendUrl = `http://${DEPLOY_HOST}:${BACKEND_PORT}/health`;
    
    // 连续请求测试
    const testCount = 5;
    let successCount = 0;
    
    for (let i = 0; i < testCount; i++) {
      try {
        // 测试前端
        const frontendResponse = await request.get(frontendUrl);
        
        // 测试后端
        const backendResponse = await request.get(backendUrl);
        
        if (frontendResponse.status() === 200 && backendResponse.status() === 200) {
          successCount++;
        }
        
        // 间隔1秒
        await new Promise(resolve => setTimeout(resolve, 1000));
        
      } catch (error) {
        console.log(`⚠️ 第${i + 1}次请求失败: ${error.message}`);
      }
    }
    
    const successRate = (successCount / testCount) * 100;
    console.log(`📊 成功率: ${successRate}% (${successCount}/${testCount})`);
    
    // 要求成功率至少80%
    expect(successRate).toBeGreaterThanOrEqual(80);
    
    console.log('✅ 服务稳定性测试通过');
  });
  
});
