#!/bin/bash

# ChatAdvisor Server 独立部署脚本
# 专用于后端服务的快速部署

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 配置变量
PROJECT_NAME="ChatAdvisor Server"
SERVICE_NAME="chat-advisor-release"
DEBUG_SERVICE_NAME="chat-advisor-debug"
BACKEND_PORT="53001"
DEBUG_PORT="33001"

# 显示帮助信息
show_help() {
    echo "ChatAdvisor Server 部署脚本"
    echo ""
    echo "用法: $0 [选项]"
    echo ""
    echo "选项:"
    echo "  --env production    部署到生产环境 (默认)"
    echo "  --env development   部署到开发环境"
    echo "  --build-only        仅构建，不部署"
    echo "  --deploy-only       仅部署，不构建"
    echo "  --migrate           运行数据库迁移"
    echo "  --help              显示此帮助信息"
    echo ""
    echo "示例:"
    echo "  $0                  # 构建并部署到生产环境"
    echo "  $0 --env development # 部署到开发环境"
    echo "  $0 --migrate        # 运行数据库迁移"
}

# 解析命令行参数
parse_args() {
    ENVIRONMENT="production"
    BUILD_ONLY=false
    DEPLOY_ONLY=false
    RUN_MIGRATION=false
    
    while [[ $# -gt 0 ]]; do
        case $1 in
            --env)
                ENVIRONMENT="$2"
                shift 2
                ;;
            --build-only)
                BUILD_ONLY=true
                shift
                ;;
            --deploy-only)
                DEPLOY_ONLY=true
                shift
                ;;
            --migrate)
                RUN_MIGRATION=true
                shift
                ;;
            --help)
                show_help
                exit 0
                ;;
            *)
                log_error "未知参数: $1"
                show_help
                exit 1
                ;;
        esac
    done
    
    # 设置环境相关变量
    if [[ "$ENVIRONMENT" == "development" ]]; then
        SERVICE_NAME="$DEBUG_SERVICE_NAME"
        BACKEND_PORT="$DEBUG_PORT"
    fi
}

# 检查项目环境
check_environment() {
    log_info "检查后端项目环境..."
    
    # 检查是否在正确的目录
    if [[ ! -f "package.json" ]]; then
        log_error "请在ChatAdvisorServer目录下运行此脚本"
        exit 1
    fi
    
    # 检查TypeScript配置
    if [[ ! -f "tsconfig.json" ]]; then
        log_error "未找到TypeScript配置文件"
        exit 1
    fi
    
    # 检查Node.js
    if ! command -v node &> /dev/null; then
        log_error "Node.js 未安装"
        exit 1
    fi
    
    # 检查npm
    if ! command -v npm &> /dev/null; then
        log_error "npm 未安装"
        exit 1
    fi
    
    # 检查PM2
    if ! command -v pm2 &> /dev/null; then
        log_error "PM2 未安装"
        exit 1
    fi
    
    local node_version=$(node --version)
    local npm_version=$(npm --version)
    
    # 检查Node.js版本
    local required_version="20"
    local current_version=$(echo $node_version | sed 's/v//' | cut -d'.' -f1)
    
    if [[ $current_version -lt $required_version ]]; then
        log_error "Node.js版本过低，要求v${required_version}+，当前版本: $node_version"
        exit 1
    fi
    
    log_info "Node.js版本: $node_version"
    log_info "npm版本: $npm_version"
    log_info "环境: $ENVIRONMENT"
    log_info "服务名: $SERVICE_NAME"
    log_info "端口: $BACKEND_PORT"
    
    log_success "环境检查完成"
}

# 检查环境配置
check_env_config() {
    log_info "检查环境配置..."
    
    if [[ -f ".env" ]]; then
        log_info "发现环境配置文件"
        
        # 检查关键配置项
        local required_vars=("OPENAI_API_KEY" "MONGODB_URI" "JWT_SECRET")
        local missing_vars=()
        
        for var in "${required_vars[@]}"; do
            if ! grep -q "^$var=" .env; then
                missing_vars+=("$var")
            fi
        done
        
        if [[ ${#missing_vars[@]} -gt 0 ]]; then
            log_warning "缺少环境变量: ${missing_vars[*]}"
        else
            log_success "环境配置检查通过"
        fi
    else
        log_warning "未找到.env配置文件"
    fi
}

# 安装依赖
install_dependencies() {
    log_info "安装后端依赖..."
    
    if [[ -f "package-lock.json" ]]; then
        if [[ "$ENVIRONMENT" == "production" ]]; then
            npm ci --only=production --silent
        else
            npm ci --silent
        fi
    else
        if [[ "$ENVIRONMENT" == "production" ]]; then
            npm install --only=production --silent
        else
            npm install --silent
        fi
    fi
    
    log_success "依赖安装完成"
}

# 构建项目
build_project() {
    if [[ "$DEPLOY_ONLY" == true ]]; then
        log_info "跳过构建阶段"
        return 0
    fi
    
    log_info "构建后端项目..."
    
    # 清理旧的构建产物
    if [[ -d "dist" ]]; then
        rm -rf dist
        log_info "已清理旧的构建产物"
    fi
    
    # TypeScript编译
    log_info "执行TypeScript编译..."
    npm run build
    
    # 验证构建结果
    if [[ ! -d "dist" ]]; then
        log_error "构建失败，未生成dist目录"
        exit 1
    fi
    
    # 检查入口文件
    local entry_files=("dist/src/index.js" "dist/index.js" "dist/app.js")
    local found_entry=false
    
    for entry in "${entry_files[@]}"; do
        if [[ -f "$entry" ]]; then
            log_info "找到入口文件: $entry"
            found_entry=true
            break
        fi
    done
    
    if [[ "$found_entry" == false ]]; then
        log_warning "未找到标准入口文件，请检查构建配置"
    fi
    
    local build_size=$(du -sh dist | cut -f1)
    local js_files=$(find dist -name "*.js" | wc -l)
    
    log_info "构建产物大小: $build_size"
    log_info "JavaScript文件数量: $js_files"
    
    log_success "项目构建完成"
}

# 运行数据库迁移
run_migration() {
    if [[ "$RUN_MIGRATION" != true ]]; then
        return 0
    fi
    
    log_info "运行数据库迁移..."
    
    # 检查是否有迁移脚本
    if npm run | grep -q "migrate"; then
        log_info "执行数据库迁移..."
        npm run migrate || log_warning "数据库迁移失败"
    else
        log_info "未找到迁移脚本，跳过迁移"
    fi
    
    log_success "数据库迁移完成"
}

# 备份当前版本
backup_current() {
    log_info "备份当前版本..."
    
    if [[ -d "dist" ]]; then
        local backup_name="dist.backup.$(date +%Y%m%d_%H%M%S)"
        cp -r dist "$backup_name"
        log_info "备份已创建: $backup_name"
        
        # 只保留最近5个备份
        ls -dt dist.backup.* 2>/dev/null | tail -n +6 | xargs rm -rf 2>/dev/null || true
        log_info "已清理旧备份"
    else
        log_info "无需备份，dist目录不存在"
    fi
}

# 部署服务
deploy_service() {
    if [[ "$BUILD_ONLY" == true ]]; then
        log_info "跳过部署阶段"
        return 0
    fi
    
    log_info "部署后端服务..."
    
    # 创建日志目录
    mkdir -p logs
    
    # 停止现有服务
    log_info "停止现有服务: $SERVICE_NAME"
    pm2 stop "$SERVICE_NAME" 2>/dev/null || log_info "服务未运行: $SERVICE_NAME"
    
    # 启动服务
    log_info "启动服务: $SERVICE_NAME"
    pm2 start pm2.config.js --only "$SERVICE_NAME"
    
    # 保存PM2配置
    pm2 save
    
    log_success "服务部署完成"
}

# 健康检查
health_check() {
    log_info "执行健康检查..."
    
    # 等待服务启动
    sleep 10
    
    local max_attempts=15
    local attempt=1
    local health_url="http://localhost:$BACKEND_PORT/health"
    local root_url="http://localhost:$BACKEND_PORT"
    
    while [[ $attempt -le $max_attempts ]]; do
        log_info "健康检查尝试 $attempt/$max_attempts..."
        
        # 先尝试健康检查端点
        if curl -f -s "$health_url" > /dev/null; then
            log_success "后端服务运行正常（健康检查端点）"
            
            # 获取健康检查响应
            local health_response=$(curl -s "$health_url" 2>/dev/null || echo "")
            if [[ -n "$health_response" ]]; then
                log_info "健康检查响应: $health_response"
            fi
            
            return 0
        # 如果健康检查端点失败，尝试根路径
        elif curl -f -s "$root_url" > /dev/null; then
            log_success "后端服务运行正常（根路径）"
            return 0
        else
            log_warning "服务未就绪，等待4秒后重试..."
            sleep 4
            ((attempt++))
        fi
    done
    
    log_error "健康检查失败，服务可能未正常启动"
    return 1
}

# 显示部署信息
show_deployment_info() {
    echo ""
    log_success "🎉 后端部署完成！"
    echo ""
    echo "部署信息:"
    echo "  - 项目: $PROJECT_NAME"
    echo "  - 环境: $ENVIRONMENT"
    echo "  - 服务: $SERVICE_NAME"
    echo "  - 端口: $BACKEND_PORT"
    echo "  - 访问地址: http://localhost:$BACKEND_PORT"
    echo "  - 健康检查: http://localhost:$BACKEND_PORT/health"
    echo ""
    echo "PM2管理命令:"
    echo "  - 查看状态: pm2 status"
    echo "  - 查看日志: pm2 logs $SERVICE_NAME"
    echo "  - 重启服务: pm2 restart $SERVICE_NAME"
    echo "  - 停止服务: pm2 stop $SERVICE_NAME"
    echo "  - 监控服务: pm2 monit"
    echo ""
    echo "日志文件:"
    echo "  - 综合日志: logs/${ENVIRONMENT}-combined.log"
    echo "  - 错误日志: logs/${ENVIRONMENT}-error.log"
    echo "  - 输出日志: logs/${ENVIRONMENT}-out.log"
    echo ""
}

# 错误处理
handle_error() {
    log_error "部署过程中发生错误"
    
    # 尝试恢复服务
    log_info "尝试恢复服务..."
    pm2 restart "$SERVICE_NAME" 2>/dev/null || log_warning "无法恢复服务"
    
    exit 1
}

# 主函数
main() {
    echo "🚀 ChatAdvisor Server 部署开始"
    echo "================================="
    
    # 设置错误处理
    trap handle_error ERR
    
    # 解析参数
    parse_args "$@"
    
    # 执行部署步骤
    check_environment
    check_env_config
    
    if [[ "$DEPLOY_ONLY" != true ]]; then
        install_dependencies
        backup_current
        build_project
        run_migration
    fi
    
    if [[ "$BUILD_ONLY" != true ]]; then
        deploy_service
        health_check
    fi
    
    show_deployment_info
    
    log_success "部署流程完成"
}

# 运行主函数
main "$@"
