# ChatAdvisor 后台管理系统 PM2 部署指南

## 概述

本文档介绍如何使用 PM2 部署 ChatAdvisor 后台管理系统，支持在 34001（生产环境）和 54001（开发环境）端口上运行。

## 端口配置

| 服务 | 端口 | 环境 | 描述 |
|------|------|------|------|
| admin-frontend-release | 34001 | 生产 | 构建后的前端静态文件服务 |
| admin-frontend-debug | 54001 | 开发 | 开发模式的前端服务 |

## 快速开始

### 1. 安装依赖

```bash
# 安装后端依赖
npm install

# 安装前端依赖
cd admin-frontend
npm install
cd ..
```

### 2. 启动服务

#### 使用 npm 脚本（推荐）

```bash
# 启动生产环境前端 (端口 34001)
npm run pm-admin:start-release

# 启动开发环境前端 (端口 54001)
npm run pm-admin:start-debug

# 启动所有前端服务
npm run pm-admin:start-all

# 查看服务状态
npm run pm-admin:status
```

#### 使用管理脚本

```bash
# 启动生产环境
./scripts/pm2-admin.sh start-release

# 启动开发环境
./scripts/pm2-admin.sh start-debug

# 查看帮助
./scripts/pm2-admin.sh help
```

### 3. 访问应用

- **生产环境**: http://localhost:34001
- **开发环境**: http://localhost:54001

## 详细命令

### PM2 管理脚本命令

```bash
# 启动服务
./scripts/pm2-admin.sh start-release    # 启动生产环境
./scripts/pm2-admin.sh start-debug      # 启动开发环境
./scripts/pm2-admin.sh start-all        # 启动所有服务

# 停止服务
./scripts/pm2-admin.sh stop-release     # 停止生产环境
./scripts/pm2-admin.sh stop-debug       # 停止开发环境
./scripts/pm2-admin.sh stop-all         # 停止所有服务

# 重启服务
./scripts/pm2-admin.sh restart-release  # 重启生产环境
./scripts/pm2-admin.sh restart-debug    # 重启开发环境
./scripts/pm2-admin.sh restart-all      # 重启所有服务

# 查看状态和日志
./scripts/pm2-admin.sh status           # 查看服务状态
./scripts/pm2-admin.sh logs-release     # 查看生产环境日志
./scripts/pm2-admin.sh logs-debug       # 查看开发环境日志

# 构建项目
./scripts/pm2-admin.sh build            # 构建前端项目
```

### NPM 脚本命令

```bash
# 前端管理
npm run pm-admin:start-release  # 启动生产环境
npm run pm-admin:start-debug    # 启动开发环境
npm run pm-admin:start-all      # 启动所有服务
npm run pm-admin:stop           # 停止所有服务
npm run pm-admin:status         # 查看状态

# 后端管理（原有）
npm run pm-release              # 启动后端生产环境
npm run pm-dev                  # 启动后端开发环境
npm run pm-all                  # 启动所有后端服务
```

## 配置说明

### PM2 配置文件

前端服务的 PM2 配置位于 `pm2.config.js` 中：

```javascript
{
    name: 'admin-frontend-release',
    script: 'npx',
    args: 'vite preview --port 34001 --host',
    cwd: './admin-frontend',
    env: {
        NODE_ENV: 'production',
        PORT: 34001
    }
}
```

### Vite 配置

前端项目的 Vite 配置会根据端口自动选择对应的后端 API：

- 端口 34001 → 后端 53001（生产）
- 端口 54001 → 后端 33001（开发）
- 其他端口 → 后端 33001（默认）

## 日志管理

### 日志文件位置

```
logs/
├── admin-release-info.log    # 生产环境综合日志
├── admin-release-err.log     # 生产环境错误日志
├── admin-release-out.log     # 生产环境输出日志
├── admin-debug-info.log      # 开发环境综合日志
├── admin-debug-err.log       # 开发环境错误日志
└── admin-debug-out.log       # 开发环境输出日志
```

### 查看日志

```bash
# 实时查看日志
pm2 logs admin-frontend-release
pm2 logs admin-frontend-debug

# 查看特定日志文件
tail -f logs/admin-release-info.log
tail -f logs/admin-debug-err.log
```

## 故障排除

### 常见问题

1. **端口被占用**
   ```bash
   # 查看端口占用
   lsof -i :34001
   lsof -i :54001
   
   # 杀死占用进程
   lsof -ti:34001 | xargs kill -9
   ```

2. **构建失败**
   ```bash
   # 手动构建前端
   cd admin-frontend
   npm run build:prod
   ```

3. **服务无法启动**
   ```bash
   # 检查 PM2 状态
   pm2 status
   
   # 查看详细错误
   pm2 logs admin-frontend-release --lines 50
   ```

### 重置服务

```bash
# 停止并删除所有前端服务
pm2 stop admin-frontend-release admin-frontend-debug
pm2 delete admin-frontend-release admin-frontend-debug

# 重新启动
./scripts/pm2-admin.sh start-all
```

## 生产部署建议

1. **使用生产环境配置**
   ```bash
   # 只启动生产环境服务
   ./scripts/pm2-admin.sh start-release
   ```

2. **设置开机自启**
   ```bash
   # 保存当前 PM2 进程列表
   pm2 save
   
   # 设置开机自启
   pm2 startup
   ```

3. **监控和告警**
   ```bash
   # 安装 PM2 监控
   pm2 install pm2-server-monit
   ```

## 更新部署

```bash
# 1. 停止服务
./scripts/pm2-admin.sh stop-all

# 2. 更新代码
git pull

# 3. 安装依赖
npm install
cd admin-frontend && npm install && cd ..

# 4. 重新启动
./scripts/pm2-admin.sh start-all
```
