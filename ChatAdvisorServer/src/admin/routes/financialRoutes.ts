/**
 * 财务管理路由
 */

import { Router } from 'express';
import { body, param, query } from 'express-validator';
import BalanceTransactionController from '../controllers/BalanceTransactionController';
import ProductController from '../controllers/ProductController';
import PricingController from '../controllers/PricingController';
import { adminAuth } from '../middleware/adminAuth';
import { validateRequest } from '../middleware/validateRequest';

const router = Router();

// 应用管理员认证中间件
router.use(adminAuth);

// ==================== 交易记录管理 ====================

// 获取交易记录列表
router.get('/transactions',
    [
        query('page').optional().isInt({ min: 1 }).withMessage('Page must be a positive integer'),
        query('limit').optional().isInt({ min: 1, max: 100 }).withMessage('Limit must be between 1 and 100'),
        query('userId').optional().isMongoId().withMessage('Invalid user ID'),
        query('type').optional().isIn(['1', '2', '3']).withMessage('Type must be 1, 2, or 3'),
        query('dateFrom').optional().isISO8601().withMessage('Invalid date format'),
        query('dateTo').optional().isISO8601().withMessage('Invalid date format'),
        query('amountMin').optional().isFloat({ min: 0 }).withMessage('Amount min must be positive'),
        query('amountMax').optional().isFloat({ min: 0 }).withMessage('Amount max must be positive'),
        query('reason').optional().isString().trim(),
        query('sortBy').optional().isIn(['timestamp', 'amount', 'type']),
        query('sortOrder').optional().isIn(['asc', 'desc'])
    ],
    validateRequest,
    BalanceTransactionController.getTransactions.bind(BalanceTransactionController)
);

// 获取交易记录详情
router.get('/transactions/:id',
    [
        param('id').isMongoId().withMessage('Invalid transaction ID')
    ],
    validateRequest,
    BalanceTransactionController.getTransactionById.bind(BalanceTransactionController)
);

// 获取用户交易历史
router.get('/transactions/user/:userId',
    [
        param('userId').isMongoId().withMessage('Invalid user ID'),
        query('page').optional().isInt({ min: 1 }).withMessage('Page must be a positive integer'),
        query('limit').optional().isInt({ min: 1, max: 100 }).withMessage('Limit must be between 1 and 100'),
        query('type').optional().isIn(['1', '2', '3']).withMessage('Type must be 1, 2, or 3'),
        query('dateFrom').optional().isISO8601().withMessage('Invalid date format'),
        query('dateTo').optional().isISO8601().withMessage('Invalid date format')
    ],
    validateRequest,
    BalanceTransactionController.getUserTransactions.bind(BalanceTransactionController)
);

// 获取交易统计信息
router.get('/transactions/stats',
    [
        query('dateFrom').optional().isISO8601().withMessage('Invalid date format'),
        query('dateTo').optional().isISO8601().withMessage('Invalid date format'),
        query('groupBy').optional().isIn(['day', 'week', 'month'])
    ],
    validateRequest,
    BalanceTransactionController.getTransactionStats.bind(BalanceTransactionController)
);

// 获取分层级交易记录
router.get('/transactions/hierarchical',
    [
        query('groupBy').optional().isIn(['date', 'user', 'type']).withMessage('GroupBy must be date, user, or type'),
        query('page').optional().isInt({ min: 1 }).withMessage('Page must be a positive integer'),
        query('limit').optional().isInt({ min: 1, max: 200 }).withMessage('Limit must be between 1 and 200'),
        query('userId').optional().isMongoId().withMessage('Invalid user ID'),
        query('type').optional().isIn(['1', '2', '3']).withMessage('Type must be 1, 2, or 3'),
        query('dateFrom').optional().isISO8601().withMessage('Invalid date format'),
        query('dateTo').optional().isISO8601().withMessage('Invalid date format')
    ],
    validateRequest,
    BalanceTransactionController.getHierarchicalTransactions.bind(BalanceTransactionController)
);

// 导出交易记录
router.get('/transactions/export',
    [
        query('format').optional().isIn(['json', 'csv']),
        query('userId').optional().isMongoId().withMessage('Invalid user ID'),
        query('type').optional().isIn(['1', '2', '3']).withMessage('Type must be 1, 2, or 3'),
        query('dateFrom').optional().isISO8601().withMessage('Invalid date format'),
        query('dateTo').optional().isISO8601().withMessage('Invalid date format'),
        query('includeUserInfo').optional().isBoolean()
    ],
    validateRequest,
    BalanceTransactionController.exportTransactions.bind(BalanceTransactionController)
);

// ==================== 产品管理 ====================

// 获取产品列表
router.get('/products',
    [
        query('page').optional().isInt({ min: 1 }).withMessage('Page must be a positive integer'),
        query('limit').optional().isInt({ min: 1, max: 100 }).withMessage('Limit must be between 1 and 100'),
        query('search').optional().isString().trim(),
        query('isEnable').optional().isBoolean(),
        query('nation').optional().isString().trim(),
        query('sortBy').optional().isIn(['createdAt', 'amount', 'productIdentifier']),
        query('sortOrder').optional().isIn(['asc', 'desc'])
    ],
    validateRequest,
    ProductController.getProducts.bind(ProductController)
);

// 获取产品详情
router.get('/products/:id',
    [
        param('id').isMongoId().withMessage('Invalid product ID')
    ],
    validateRequest,
    ProductController.getProductById.bind(ProductController)
);

// 创建产品
router.post('/products',
    [
        body('productIdentifier').notEmpty().withMessage('Product identifier is required'),
        body('amount').isFloat({ min: 0 }).withMessage('Amount must be a positive number'),
        body('isEnable').optional().isBoolean(),
        body('nation').optional().isString().trim()
    ],
    validateRequest,
    ProductController.createProduct.bind(ProductController)
);

// 更新产品
router.put('/products/:id',
    [
        param('id').isMongoId().withMessage('Invalid product ID'),
        body('productIdentifier').optional().notEmpty().withMessage('Product identifier cannot be empty'),
        body('amount').optional().isFloat({ min: 0 }).withMessage('Amount must be a positive number'),
        body('isEnable').optional().isBoolean(),
        body('nation').optional().isString().trim()
    ],
    validateRequest,
    ProductController.updateProduct.bind(ProductController)
);

// 删除产品
router.delete('/products/:id',
    [
        param('id').isMongoId().withMessage('Invalid product ID')
    ],
    validateRequest,
    ProductController.deleteProduct.bind(ProductController)
);

// 批量更新产品状态
router.post('/products/batch-status',
    [
        body('productIds').isArray().withMessage('Product IDs must be an array'),
        body('productIds.*').isMongoId().withMessage('Invalid product ID'),
        body('isEnable').isBoolean().withMessage('isEnable must be a boolean')
    ],
    validateRequest,
    ProductController.batchUpdateProductStatus.bind(ProductController)
);

// 批量删除产品
router.post('/products/batch-delete',
    [
        body('productIds').isArray().withMessage('Product IDs must be an array'),
        body('productIds.*').isMongoId().withMessage('Invalid product ID')
    ],
    validateRequest,
    ProductController.batchDeleteProducts.bind(ProductController)
);

// 获取产品统计信息
router.get('/products/stats', ProductController.getProductStats.bind(ProductController));

// 导出产品数据
router.get('/products/export',
    [
        query('format').optional().isIn(['json', 'csv']),
        query('isEnable').optional().isBoolean(),
        query('nation').optional().isString().trim()
    ],
    validateRequest,
    ProductController.exportProducts.bind(ProductController)
);

// 复制产品
router.post('/products/:id/duplicate',
    [
        param('id').isMongoId().withMessage('Invalid product ID'),
        body('newProductIdentifier').notEmpty().withMessage('New product identifier is required')
    ],
    validateRequest,
    ProductController.duplicateProduct.bind(ProductController)
);

// ==================== 定价管理 ====================

// 获取定价列表
router.get('/pricing',
    [
        query('page').optional().isInt({ min: 1 }).withMessage('Page must be a positive integer'),
        query('limit').optional().isInt({ min: 1, max: 100 }).withMessage('Limit must be between 1 and 100'),
        query('search').optional().isString().trim(),
        query('sortBy').optional().isIn(['createdAt', 'modelName', 'inPrice', 'outPrice']),
        query('sortOrder').optional().isIn(['asc', 'desc'])
    ],
    validateRequest,
    PricingController.getPricings.bind(PricingController)
);

// 获取定价详情
router.get('/pricing/:id',
    [
        param('id').isMongoId().withMessage('Invalid pricing ID')
    ],
    validateRequest,
    PricingController.getPricingById.bind(PricingController)
);

// 创建定价
router.post('/pricing',
    [
        body('modelName').notEmpty().withMessage('Model name is required'),
        body('inPrice').isFloat({ min: 0 }).withMessage('Input price must be a positive number'),
        body('outPrice').isFloat({ min: 0 }).withMessage('Output price must be a positive number'),
        body('count').isInt({ min: 0 }).withMessage('Count must be a positive integer'),
        body('alias').optional().isObject(),
        body('intro').optional().isObject()
    ],
    validateRequest,
    PricingController.createPricing.bind(PricingController)
);

// 更新定价
router.put('/pricing/:id',
    [
        param('id').isMongoId().withMessage('Invalid pricing ID'),
        body('modelName').optional().notEmpty().withMessage('Model name cannot be empty'),
        body('inPrice').optional().isFloat({ min: 0 }).withMessage('Input price must be a positive number'),
        body('outPrice').optional().isFloat({ min: 0 }).withMessage('Output price must be a positive number'),
        body('count').optional().isInt({ min: 0 }).withMessage('Count must be a positive integer'),
        body('alias').optional().isObject(),
        body('intro').optional().isObject()
    ],
    validateRequest,
    PricingController.updatePricing.bind(PricingController)
);

// 删除定价
router.delete('/pricing/:id',
    [
        param('id').isMongoId().withMessage('Invalid pricing ID')
    ],
    validateRequest,
    PricingController.deletePricing.bind(PricingController)
);

// 批量更新定价
router.post('/pricing/batch-update',
    [
        body('pricingIds').isArray().withMessage('Pricing IDs must be an array'),
        body('pricingIds.*').isMongoId().withMessage('Invalid pricing ID'),
        body('updates').isObject().withMessage('Updates must be an object')
    ],
    validateRequest,
    PricingController.batchUpdatePricing.bind(PricingController)
);

// 获取定价统计信息
router.get('/pricing/stats', PricingController.getPricingStats.bind(PricingController));

// 导出定价数据
router.get('/pricing/export',
    [
        query('format').optional().isIn(['json', 'csv']),
        query('includeUsage').optional().isBoolean()
    ],
    validateRequest,
    PricingController.exportPricings.bind(PricingController)
);

// 复制定价模型
router.post('/pricing/:id/duplicate',
    [
        param('id').isMongoId().withMessage('Invalid pricing ID'),
        body('newModelName').notEmpty().withMessage('New model name is required')
    ],
    validateRequest,
    PricingController.duplicatePricing.bind(PricingController)
);

export default router;
