import mongoose, { Document, Model, Schema } from 'mongoose';

// 定义模型的 TypeScript 接口
export interface IPricing extends Document {
    modelName: string;
    inPrice: number;
    outPrice: number;
    count: number;
    alias: {
        [language: string]: string;
    };
    intro: {
        [language: string]: string;
    };
}

// 创建价格模式
const pricingSchema = new Schema({
    modelName: { type: String, required: true, unique: false },
    inPrice: { type: Number, required: true },
    outPrice: { type: Number, required: true },
    count: { type: Number, required: true },
    alias: {
        type: Map,
        of: String,
        required: true
    },
    intro: {
        type: Map,
        of: String,
        required: false
    }
},{
    versionKey: false
});

const modelName = 'Pricing';

// 检查模型是否已经存在，如果存在则使用已存在的模型
const Pricing: Model<IPricing> = mongoose.models[modelName] || mongoose.model<IPricing>(modelName, pricingSchema);
export default Pricing;