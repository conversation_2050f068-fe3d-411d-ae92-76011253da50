import { Express } from 'express';
import BaseRouter from './base';
import AuthRouter from './auth';
import ChatRouter from './chat';
import ConfigRouter from './config';
import UploadRouter from './upload';
import SupportRouter from './support';
import CompatibilityRouter from './compatibility';

// 第三方登录路由
import AppleRouter from './apple';
import GoogleRouter from './google';
import TikTokRouter from './tiktok';
import TwitterRouter from './twitter';
import FacebookRouter from './facebook';

/**
 * 配置所有应用路由
 * @param app Express应用实例
 */
export function configureRoutes(app: Express): void {
    // 1. 基础路由（健康检查、静态资源等）
    app.use('/', BaseRouter);

    // 2. 配置路由（不需要认证）
    app.use('/api', ConfigRouter);

    // 3. 认证相关路由（不需要预先认证）
    app.use('/api/auth', AuthRouter);

    // 4. 第三方登录路由（不需要预先认证）
    app.use('/api/oauth', AppleRouter);
    app.use('/api/oauth', GoogleRouter);
    app.use('/api/oauth', TikTokRouter);
    app.use('/api/oauth', TwitterRouter);
    app.use('/api/oauth', FacebookRouter);

    // 5. 兼容旧的第三方登录路径（直接在根路径下）
    app.use('/', AppleRouter);
    app.use('/', GoogleRouter);
    app.use('/', TikTokRouter);
    app.use('/', TwitterRouter);
    app.use('/', FacebookRouter);

    // 6. 兼容性路由（支持旧版本客户端API路径）
    app.use('/', CompatibilityRouter);

    // 7. 需要认证的业务路由
    app.use('/api', ChatRouter);
    app.use('/api', UploadRouter);
    app.use('/api', SupportRouter);
}

/**
 * 路由信息配置
 */
export const routeInfo = {
    // 不需要认证的路由
    publicRoutes: [
        // 基础路由
        '/',
        '/health',
        '/favicon.ico',
        '/robots.txt',
        '/app-ads.txt',

        // 后台管理相关（静态资源和登录）
        '/admin',
        '/admin/',
        '/admin/*',
        '/api/admin/auth/login',
        '/api/admin/auth/me',

        // 浏览器相关请求
        '/.well-known/*',
        '/.well-known/appspecific/*',

        // 配置路由
        '/api/config',
        '/api/getConfig',
        '/api/getProducts',
        '/api/getPricing',
        '/api/checkVersion',
        '/api/versionInfo',

        // 认证路由
        '/api/auth/login',
        '/api/auth/register',
        '/api/auth/sendEmailCode',
        '/api/auth/verifyEmailCode',
        '/api/auth/refreshToken',

        // 兼容性认证路由（直接在根路径下）
        '/login',
        '/register',
        '/sendEmailCode',
        '/verifyEmailCode',
        '/refreshToken',

        // 第三方登录
        '/api/oauth/appleLogin',
        '/api/oauth/googleLogin',
        '/api/oauth/tiktokLogin',
        '/api/oauth/tiktoklogin', // TikTok回调
        '/api/oauth/twitterLogin',
        '/api/oauth/facebookLogin',

        // 兼容旧的苹果登录路径
        '/appleLogin',
        '/googleLogin',
        '/tiktokLogin',
        '/twitterLogin',
        '/facebookLogin',

        // 支付相关
        '/api/oauth/verifyPurchase',
        '/api/oauth/refund',
        '/api/verifyPurchase',
        '/api/refund',

        // Apple相关
        '/api/oauth/apple-app-site-association',
        '/apple-app-site-association',

        // 其他公开接口
        '/api/generateQuestion',
        '/api/deletion',
        '/api/verify-deletion',
        '/api/facebook-data-deletion',

        // 兼容性公开接口
        '/generateQuestion',
        '/checkVersion',
        '/versionInfo',

        // 静态页面
        '/privacy.html',
        '/userTerms.html',
        '/support.html'
    ],
    
    // 需要认证的路由
    protectedRoutes: [
        '/api/chat',
        '/api/generateTitle',
        '/api/upload',
        '/api/userBalance',
        '/api/auth/deleteAccount',
        // 兼容性路由（需要认证）
        '/userBalance',
        '/chat',
        '/generateTitle',
        '/deleteAccount',
        '/uploadAudio',
        '/uploadAudioChat'
    ]
};

export default configureRoutes;
