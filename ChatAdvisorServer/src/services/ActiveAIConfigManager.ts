import ActiveAIConfig, { IActiveAIConfig } from '../models/ActiveAIConfig';
import AIServiceConfig from '../models/AIServiceConfig';
import AIServiceModel from '../models/AIServiceModel';
import { logger } from '../business/logger';
import AIConfigCache from './AIConfigCache';
import OpenAIClientFactory from './OpenAIClientFactory';

class ActiveAIConfigManager {
    /**
     * 获取当前启用的配置
     */
    async getActiveConfig(): Promise<IActiveAIConfig | null> {
        try {
            const activeConfig = await ActiveAIConfig.getActiveConfig();
            
            if (activeConfig) {
                logger.debug(`获取启用配置: ${activeConfig.configId} - ${activeConfig.modelId}`);
            } else {
                logger.debug('未找到启用的AI配置');
            }
            
            return activeConfig;
        } catch (error) {
            logger.error('获取启用配置失败:', error);
            return null;
        }
    }

    /**
     * 启用指定的配置和模型
     */
    async activateConfig(configId: string, modelId: string, activatedBy: string): Promise<IActiveAIConfig> {
        try {
            // 验证配置是否存在且启用
            const config = await AIServiceConfig.findById(configId);
            if (!config) {
                throw new Error(`AI服务配置不存在: ${configId}`);
            }
            if (!config.isActive) {
                throw new Error(`AI服务配置已禁用: ${config.name}`);
            }

            // 验证模型是否存在且属于该配置
            const model = await AIServiceModel.findOne({ 
                _id: modelId, 
                configId: configId,
                isActive: true 
            });
            if (!model) {
                throw new Error(`模型不存在或已禁用: ${modelId}`);
            }

            // 设置启用配置
            const activeConfig = await ActiveAIConfig.setActiveConfig(configId, modelId, activatedBy);
            
            logger.info(`启用AI配置: ${config.name} - ${model.displayName} (${configId}/${modelId})`);
            
            // 清除相关缓存
            await this.clearCache();
            
            return activeConfig;
        } catch (error) {
            logger.error('启用AI配置失败:', error);
            throw error;
        }
    }

    /**
     * 禁用当前配置
     */
    async deactivateConfig(): Promise<void> {
        try {
            await ActiveAIConfig.clearActiveConfig();
            
            logger.info('已禁用当前AI配置');
            
            // 清除相关缓存
            await this.clearCache();
        } catch (error) {
            logger.error('禁用AI配置失败:', error);
            throw error;
        }
    }

    /**
     * 获取配置的可用模型列表
     */
    async getAvailableModels(configId: string): Promise<any[]> {
        try {
            const models = await AIServiceModel.find({ 
                configId: configId,
                isActive: true 
            }).sort({ sortOrder: 1, displayName: 1 });
            
            return models.map(model => ({
                _id: model._id,
                modelName: model.modelName,
                displayName: model.displayName,
                description: model.description,
                maxTokens: model.maxTokens,
                pricing: model.pricing
            }));
        } catch (error) {
            logger.error('获取可用模型失败:', error);
            throw error;
        }
    }

    /**
     * 检查配置是否为当前启用配置
     */
    async isConfigActive(configId: string): Promise<boolean> {
        try {
            const activeConfig = await this.getActiveConfig();
            return activeConfig?.configId?.toString() === configId;
        } catch (error) {
            logger.error('检查配置启用状态失败:', error);
            return false;
        }
    }

    /**
     * 获取启用配置的统计信息
     */
    async getActiveConfigStats(): Promise<any> {
        try {
            const activeConfig = await this.getActiveConfig();
            
            if (!activeConfig) {
                return {
                    hasActiveConfig: false,
                    configName: null,
                    modelName: null,
                    activatedAt: null,
                    activatedBy: null
                };
            }

            return {
                hasActiveConfig: true,
                configId: (activeConfig.configId as any)._id,
                configName: (activeConfig.configId as any).name,
                modelId: (activeConfig.modelId as any)._id,
                modelName: (activeConfig.modelId as any).displayName,
                activatedAt: activeConfig.activatedAt,
                activatedBy: activeConfig.activatedBy.toString()
            };
        } catch (error) {
            logger.error('获取启用配置统计失败:', error);
            throw error;
        }
    }

    /**
     * 清除相关缓存
     */
    private async clearCache(): Promise<void> {
        try {
            await AIConfigCache.clearConfigCache();
            OpenAIClientFactory.clearClientCache();
            logger.debug('已清除AI配置相关缓存');
        } catch (error) {
            logger.error('清除缓存失败:', error);
        }
    }
}

export default new ActiveAIConfigManager();
