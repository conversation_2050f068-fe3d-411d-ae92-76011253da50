// ChatAdvisor Server PM2 配置
// 专用于后端服务的独立部署和管理

module.exports = {
    apps: [
        {
            name: 'chat-advisor-release',
            script: 'dist.current/src/index.js',
            watch: false,
            autorestart: true,
            max_restarts: 10,
            min_uptime: '10s',
            env: {
                NODE_ENV: 'production',
                PORT: 53001
            },
            log_date_format: 'YYYY-MM-DD HH:mm:ss Z',
            log_file: 'logs/release-combined.log',
            error_file: 'logs/release-error.log',
            out_file: 'logs/release-out.log',
            merge_logs: true,
            instances: 1,
            exec_mode: 'fork',
            // 健康检查配置
            health_check_url: 'http://localhost:53001/health',
            health_check_grace_period: 3000,
        },
        {
            name: 'chat-advisor-debug',
            script: 'dist.current/src/index.js',
            watch: true,
            autorestart: true,
            max_restarts: 10,
            min_uptime: '10s',
            env: {
                NODE_ENV: 'debug',
                PORT: 33001
            },
            ignore_watch: ['logs', 'dist', 'dist.current', 'config.json', 'public/assets','uploads'],
            log_date_format: 'YYYY-MM-DD HH:mm:ss Z',
            log_file: 'logs/debug-combined.log',
            error_file: 'logs/debug-error.log',
            out_file: 'logs/debug-out.log',
            merge_logs: true,
            instances: 1,
            exec_mode: 'fork',
            // 健康检查配置
            health_check_url: 'http://localhost:33001/health',
            health_check_grace_period: 3000,
        }
    ],

    // 部署配置
    deploy: {
        production: {
            user: 'root',
            host: '**************',
            ref: 'origin/main',
            repo: '**********************:server/chatadvisor-server.git',
            path: '/opt/chatadvisor/ChatAdvisorServer',
            'pre-deploy-local': '',
            'post-deploy': 'npm install --production && npm run build && pm2 reload ecosystem.config.js --env production',
            'pre-setup': ''
        }
    }
};
