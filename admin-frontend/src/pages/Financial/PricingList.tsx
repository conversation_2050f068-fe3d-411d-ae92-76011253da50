import React, { useState, useEffect, useCallback } from 'react';
import { format } from 'date-fns';
import { useApi } from '@/hooks/useApi';
import { useTable } from '@/hooks/useTable';
import { financialService } from '@/services/financial';
import { Pricing } from '@/types/financial';
import { TableColumn } from '@/types/common';
import Table from '@/components/common/Table';
import Button from '@/components/common/Button';
import Badge from '@/components/common/Badge';
import Loading from '@/components/common/Loading';

const PricingList: React.FC = () => {
  const [pricings, setPricings] = useState<Pricing[]>([]);
  const [total, setTotal] = useState(0);

  // 表格状态管理
  const {
    tableState,
    pagination,
    sorting,
    searching,
    getQueryParams,
  } = useTable({
    initialPageSize: 20,
    initialSortBy: 'createdAt',
    initialSortOrder: 'desc',
  });

  // API调用
  const {
    loading,
    execute: fetchPricings,
  } = useApi(financialService.getPricings);

  // 加载定价列表
  const loadPricings = useCallback(async () => {
    try {
      const params = getQueryParams();
      const result = await fetchPricings(params);
      setPricings(result.items);
      setTotal(result.total);
    } catch (error) {
      console.error('加载定价列表失败:', error);
    }
  }, [fetchPricings, getQueryParams]);

  // 初始加载和参数变化时重新加载
  useEffect(() => {
    loadPricings();
  }, [loadPricings]);

  // 表格列定义
  const columns: TableColumn<Pricing>[] = [
    {
      key: 'modelName',
      title: '模型名称',
      dataIndex: 'modelName',
      width: 150,
      render: (modelName: string) => (
        <span className="text-sm font-medium text-gray-900">
          {modelName}
        </span>
      ),
    },
    {
      key: 'displayName',
      title: '显示名称',
      dataIndex: 'displayName',
      render: (displayName: string, record: Pricing) => (
        <div className="text-sm">
          <div className="font-medium text-gray-900">{displayName}</div>
          <div className="text-gray-500">{record.description}</div>
        </div>
      ),
    },
    {
      key: 'inputPrice',
      title: '输入价格',
      dataIndex: 'inputPrice',
      width: 120,
      sortable: true,
      render: (inputPrice: number) => (
        <span className="text-sm font-medium text-gray-900">
          ¥{inputPrice?.toFixed(6) || '0.000000'}/1K tokens
        </span>
      ),
    },
    {
      key: 'outputPrice',
      title: '输出价格',
      dataIndex: 'outputPrice',
      width: 120,
      sortable: true,
      render: (outputPrice: number) => (
        <span className="text-sm font-medium text-gray-900">
          ¥{outputPrice?.toFixed(6) || '0.000000'}/1K tokens
        </span>
      ),
    },
    {
      key: 'isEnabled',
      title: '状态',
      dataIndex: 'isEnabled',
      width: 100,
      render: (isEnabled: boolean) => (
        <Badge variant={isEnabled ? 'success' : 'default'}>
          {isEnabled ? '启用' : '禁用'}
        </Badge>
      ),
    },
    {
      key: 'usageCount',
      title: '使用次数',
      dataIndex: 'usageCount',
      width: 100,
      sortable: true,
      render: (usageCount: number) => (
        <span className="text-sm text-gray-900">
          {usageCount || 0}
        </span>
      ),
    },
    {
      key: 'totalTokens',
      title: '总Token数',
      dataIndex: 'totalTokens',
      width: 120,
      sortable: true,
      render: (totalTokens: number) => (
        <span className="text-sm text-gray-900">
          {totalTokens ? (totalTokens / 1000).toFixed(1) + 'K' : '0'}
        </span>
      ),
    },
    {
      key: 'revenue',
      title: '收入',
      dataIndex: 'revenue',
      width: 120,
      sortable: true,
      render: (revenue: number) => (
        <span className="text-sm font-medium text-green-600">
          ¥{revenue?.toFixed(2) || '0.00'}
        </span>
      ),
    },
    {
      key: 'createdAt',
      title: '创建时间',
      dataIndex: 'createdAt',
      width: 150,
      sortable: true,
      render: (createdAt: string) => (
        <span className="text-sm text-gray-500">
          {createdAt ? format(new Date(createdAt), 'yyyy-MM-dd HH:mm') : '未知'}
        </span>
      ),
    },
    {
      key: 'actions',
      title: '操作',
      dataIndex: '_id',
      width: 150,
      render: (id: string, record: Pricing) => (
        <div className="flex space-x-2">
          <Button size="sm" variant="secondary">
            查看
          </Button>
          <Button size="sm" variant="secondary">
            编辑
          </Button>
          <Button size="sm" variant="danger">
            删除
          </Button>
        </div>
      ),
    },
  ];

  if (loading) {
    return <Loading />;
  }

  return (
    <div className="space-y-6">
      {/* 页面标题 */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-semibold text-gray-900">定价管理</h1>
          <p className="mt-1 text-sm text-gray-600">
            设置和调整产品定价策略
          </p>
        </div>
        <div className="flex space-x-3">
          <Button variant="secondary">
            导出数据
          </Button>
          <Button>
            新增定价
          </Button>
        </div>
      </div>

      {/* 搜索和筛选 */}
      <div className="bg-white p-4 rounded-lg shadow space-y-4">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              搜索
            </label>
            <div className="relative">
              <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <svg className="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                </svg>
              </div>
              <input
                type="text"
                placeholder="搜索模型名称..."
                value={searching.searchTerm}
                onChange={(e) => searching.setSearchTerm(e.target.value)}
                className="pl-10 w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>
          </div>
          
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              状态
            </label>
            <select className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500">
              <option value="">所有状态</option>
              <option value="enabled">启用</option>
              <option value="disabled">禁用</option>
            </select>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              价格范围
            </label>
            <select className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500">
              <option value="">所有价格</option>
              <option value="low">低价（&lt;0.01）</option>
              <option value="medium">中价（0.01-0.1）</option>
              <option value="high">高价（&gt;0.1）</option>
            </select>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              排序
            </label>
            <select className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500">
              <option value="createdAt_desc">创建时间（新到旧）</option>
              <option value="createdAt_asc">创建时间（旧到新）</option>
              <option value="inputPrice_desc">输入价格（高到低）</option>
              <option value="inputPrice_asc">输入价格（低到高）</option>
              <option value="usageCount_desc">使用次数（高到低）</option>
              <option value="revenue_desc">收入（高到低）</option>
            </select>
          </div>
        </div>
      </div>

      {/* 定价列表表格 */}
      <Table
        columns={columns}
        data={pricings}
        loading={loading}
        emptyText="暂无定价配置"
        rowKey="_id"
      />

      {/* 分页 */}
      <div className="flex justify-between items-center">
        <div className="text-sm text-gray-700">
          共 {total} 条记录，第 {pagination.currentPage} / {Math.ceil(total / pagination.pageSize)} 页
        </div>
        <div className="flex space-x-2">
          <Button
            variant="secondary"
            size="sm"
            disabled={pagination.currentPage <= 1}
            onClick={() => pagination.setCurrentPage(pagination.currentPage - 1)}
          >
            上一页
          </Button>
          <Button
            variant="secondary"
            size="sm"
            disabled={pagination.currentPage >= Math.ceil(total / pagination.pageSize)}
            onClick={() => pagination.setCurrentPage(pagination.currentPage + 1)}
          >
            下一页
          </Button>
        </div>
      </div>
    </div>
  );
};

export default PricingList;
