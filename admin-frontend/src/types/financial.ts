import { PaginationParams, SortParams, SearchParams } from './common';

// 交易类型枚举
export enum TransactionType {
  Deposit = 1,    // 充值
  In = 2,         // 入账
  Out = 3         // 出账/消费
}

// 余额交易记录
export interface BalanceTransaction {
  _id: string;
  userId: string;
  amount: number;
  reason: string;
  timestamp: string;
  modelId?: string;
  type: TransactionType;
  user?: {
    _id: string;
    email: string;
    fullName?: string;
    balance: number;
  };
  pricing?: {
    _id: string;
    modelName: string;
    alias: Record<string, string>;
    intro?: Record<string, string>;
  };
  typeLabel?: string;
}

// 交易记录查询参数
export interface TransactionListParams extends PaginationParams, SortParams, SearchParams {
  userId?: string;
  type?: TransactionType;
  dateFrom?: string;
  dateTo?: string;
  amountMin?: number;
  amountMax?: number;
  reason?: string;
  modelId?: string;
}

// 交易统计信息
export interface TransactionStats {
  overview: {
    totalTransactions: number;
    totalAmount: number;
    avgAmount: number;
    deposits: number;
    consumptions: number;
  };
  typeDistribution: {
    _id: TransactionType;
    count: number;
    totalAmount: number;
    avgAmount: number;
    typeLabel: string;
  }[];
  timeSeriesData: {
    date: string;
    deposits: number;
    consumptions: number;
    netAmount: number;
  }[];
  topUsers: {
    userId: string;
    user: {
      email: string;
      fullName?: string;
    };
    totalAmount: number;
    transactionCount: number;
  }[];
  modelUsage: {
    modelId: string;
    modelName: string;
    usageCount: number;
    totalRevenue: number;
    avgTransactionAmount: number;
  }[];
  groupBy: 'day' | 'week' | 'month';
  dateRange: {
    from: string;
    to: string;
  };
}

// 产品信息
export interface Product {
  _id: string;
  productIdentifier: string;
  amount: number;
  isEnable: boolean;
  nation: string;
  createdAt?: string;
  updatedAt?: string;
}

// 产品查询参数
export interface ProductListParams extends PaginationParams, SortParams, SearchParams {
  isEnable?: boolean;
  nation?: string;
}

// 产品表单数据
export interface ProductFormData {
  productIdentifier: string;
  amount: number;
  isEnable: boolean;
  nation: string;
}

// 产品统计信息
export interface ProductStats {
  totalProducts: number;
  enabledProducts: number;
  disabledProducts: number;
  totalValue: number;
  avgAmount: number;
  nationDistribution: {
    nation: string;
    count: number;
    percentage: number;
  }[];
  amountRanges: {
    range: string;
    count: number;
    percentage: number;
  }[];
}

// 定价模型
export interface Pricing {
  _id: string;
  modelName: string;
  inPrice: number;
  outPrice: number;
  count: number;
  alias: Record<string, string>;
  intro?: Record<string, string>;
  createdAt?: string;
  updatedAt?: string;
  usageStats?: {
    totalUsage: number;
    totalRevenue: number;
    avgUsage: number;
  };
}

// 定价查询参数
export interface PricingListParams extends PaginationParams, SortParams, SearchParams {
  modelName?: string;
}

// 定价表单数据
export interface PricingFormData {
  modelName: string;
  inPrice: number;
  outPrice: number;
  count: number;
  alias: Record<string, string>;
  intro?: Record<string, string>;
}

// 定价统计信息
export interface PricingStats {
  overview: {
    totalPricings: number;
    totalRevenue: number;
    totalTransactions: number;
    avgRevenue: number;
  };
  priceRanges: {
    _id: string;
    count: number;
    range: string;
  }[];
  topModels: {
    _id: string;
    modelName: string;
    usageCount: number;
    totalRevenue: number;
    avgTransactionAmount: number;
  }[];
}

// 批量操作参数
export interface BatchProductOperation {
  productIds: string[];
  operation: 'enable' | 'disable' | 'delete';
}

export interface BatchPricingOperation {
  pricingIds: string[];
  updates: Partial<PricingFormData>;
}

// 财务概览数据
export interface FinancialOverview {
  totalRevenue: number;
  totalTransactions: number;
  activeProducts: number;
  activePricingModels: number;
  revenueGrowth: number;
  transactionGrowth: number;
  topRevenueModel: {
    modelName: string;
    revenue: number;
  };
  recentTransactions: BalanceTransaction[];
}

// 收入分析数据
export interface RevenueAnalysis {
  timeRange: {
    start: string;
    end: string;
  };
  totalRevenue: number;
  revenueByModel: {
    modelName: string;
    revenue: number;
    percentage: number;
    transactionCount: number;
  }[];
  revenueByTimeUnit: {
    period: string;
    revenue: number;
    transactionCount: number;
    avgTransactionAmount: number;
  }[];
  revenueByUserType: {
    userType: string;
    revenue: number;
    userCount: number;
    avgRevenuePerUser: number;
  }[];
}

// 分层级交易记录相关类型
export interface HierarchicalTransactionParams {
  groupBy?: 'date' | 'user' | 'type';
  page?: number;
  limit?: number;
  userId?: string;
  type?: TransactionType;
  dateFrom?: string;
  dateTo?: string;
}

// 用户交易分组
export interface UserTransactionGroup {
  user: {
    _id: string;
    email: string;
    fullName?: string;
    balance?: number;
  };
  transactions: BalanceTransaction[];
  totalAmount: number;
  totalCount: number;
  typeStats: {
    deposit: number;
    in: number;
    out: number;
  };
  dateGroups?: DateTransactionGroup[];
  isExpanded?: boolean;
}

// 日期交易分组
export interface DateTransactionGroup {
  date: string;
  transactions?: BalanceTransaction[];
  totalAmount: number;
  totalCount: number;
  count?: number;
  users?: UserTransactionGroup[];
  isExpanded?: boolean;
}

// 类型交易分组
export interface TypeTransactionGroup {
  type: TransactionType;
  typeLabel: string;
  transactions: BalanceTransaction[];
  totalAmount: number;
  totalCount: number;
  users: UserTransactionGroup[];
  isExpanded?: boolean;
}

// 分层级交易数据
export interface HierarchicalTransactionData {
  hierarchicalData: DateTransactionGroup[] | UserTransactionGroup[] | TypeTransactionGroup[];
  groupBy: 'date' | 'user' | 'type';
  pagination: {
    page: number;
    limit: number;
    total: number;
    pages: number;
  };
}

// 导出参数
export interface ExportParams {
  format: 'json' | 'csv';
  dateFrom?: string;
  dateTo?: string;
  includeUserInfo?: boolean;
}
